<odoo>
    <data>
        <!-- Collection error -->
        <record id="collection_error_101" model="rb_delivery.error_log">
            <field name="error_title">Permission denied while editing money collection</field>
            <field name="name">You can't edit the collection of sequence {collection_sequence} while the status is completed</field>
            <field name="what_to_do">Ask from Administrator to allow you to do that change</field>
            <field name="error_code">101</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_102" model="rb_delivery.error_log">
            <field name="error_title">Can't add order/orders to money collection</field>
            <field name="name">Order with number {order_sequence} already exist in collection with sequence {collection_sequence}</field>
            <field name="what_to_do">You can go and detach orders from collections</field>
            <field name="error_code">102</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_103" model="rb_delivery.error_log">
            <field name="error_title">Can't add order/orders to money collection</field>
            <field name="name">You can not add orders to money collection, unless order's state should be in {collection_status}</field>
            <field name="what_to_do">Go to configuraions called "collection_status" and add the status of the order you are trying to add to collection or contact support team</field>
            <field name="error_code">103</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_104" model="rb_delivery.error_log">
            <field name="error_title">Can not add orders to money collection</field>
            <field name="name">You can not add the order to the collection unless the order's Sender is {collection_sender}.</field>
            <field name="what_to_do">You can either change the order's Sender to {collection_sender}, or create a new collection for the order of sequence {order_sequence}.</field>
            <field name="error_code">104</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_105" model="rb_delivery.error_log">
            <field name="error_title">Can not remove order from collection</field>
            <field name="name">Can not remove order {order_sequence} from the collection of sequence {collection_sequence} unless order status in {order_status} status</field>
            <field name="what_to_do">Go to configuration called "prevent_detach_order_from_collection" and remove the status of the order you are trying to detach  or contact support team</field>
            <field name="error_code">105</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>


        <record id="collection_error_107" model="rb_delivery.error_log">
            <field name="error_title">Error while updating orders from money collection</field>
            <field name="name">Collection status {collection_status} related to {order_status} order status and this status is not found in order statuses.</field>
            <field name="what_to_do">Check {order_status} if active in order statuses or remove {order_status} from related order status in collection status</field>
            <field name="error_code">107</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_108" model="rb_delivery.error_log">
            <field name="error_title">Can not create money collection</field>
            <field name="name">These orders {order_sequences} exist in money collection of sequences {collection_sequences}.</field>
            <field name="what_to_do">Remove orders from money collections they exist in, before trying again to create a money collection.</field>
            <field name="error_code">108</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_109" model="rb_delivery.error_log">
            <field name="error_title">Can not create money collection</field>
            <field name="name">Order {order_sequence} should have a business when added to money collection.</field>
            <field name="what_to_do">Go to order of sequence {order_sequence} and add a business to it.</field>
            <field name="error_code">109</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_110" model="rb_delivery.error_log">
            <field name="error_title">Can not create money collection</field>
            <field name="name">There is no statuses in the configuration for money collection named "collection_status".</field>
            <field name="what_to_do">Please contact your adminstrator to add states for collection configuration named "collection_status".</field>
            <field name="error_code">110</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_111" model="rb_delivery.error_log">
            <field name="error_title">Can not create money collection</field>
            <field name="name">To create money collection, order status must be in {order_statuses}</field>
            <field name="what_to_do">Go to order of sequence {order_sequence} and change its status to one of the statuses {order_statuses} or go to "collection_statuses" settings and add the order status {order_status} to it</field>
            <field name="error_code">111</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_112" model="rb_delivery.error_log">
            <field name="error_title">You can not change collection status</field>
            <field name="name">You are not allowed to change to this state {collection_status}.</field>
            <field name="what_to_do">Make sure you have access to change to status {collection_status}, or contact adminstration for more information.</field>
            <field name="error_code">112</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_113" model="rb_delivery.error_log">
            <field name="error_title">Can not update money collection status</field>
            <field name="name">You are not allowed to change from this status {current_status} to this status {new_status}.</field>
            <field name="what_to_do">Make sure the status you are trying to change to is one of the next statsues for the status {collection_status}, or contact adminstration for more information.</field>
            <field name="error_code">113</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="collection_error_114" model="rb_delivery.error_log">
            <field name="error_title">Error while printing money collection report</field>
            <field name="name">Assignment should be either agent or sender, please contact administration.</field>
            <field name="what_to_do">Assignemt should be either driver or business, please contact administration.</field>
            <field name="error_code">114</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.multi_print_orders_money_collector</field>
        </record>

        <record id="order_error_162" model="rb_delivery.error_log">
            <field name="error_title">Cant create money collection</field>
            <field name="name">Error while creating money collection because there is no business in the orders with sequences {order_sequences}.</field>
            <field name="what_to_do">Go to the orders with sequences {order_sequences} and add a business to them.</field>
            <field name="error_code">162</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>


        <!-- Order error -->
        <record id="order_error_201" model="rb_delivery.error_log">
            <field name="error_title">Can not edit reference number</field>
            <field name="name">Reference ID {reference_id} is not correct.</field>
            <field name="what_to_do">Make sure the Reference ID does not contain any Invalid characters OR edit the configuaration for reference ID in "allow_reference_id_accept_the_english_letter"".</field>
            <field name="error_code">201</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_202" model="rb_delivery.error_log">
            <field name="error_title">Error while updating Sequence</field>
            <field name="name">Sequence {sequence} is not correct.</field>
            <field name="what_to_do">Make sure the {sequence} does not contain any Invalid characters.</field>
            <field name="error_code">202</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_203" model="rb_delivery.error_log">
            <field name="error_title">Error while updating Reference ID</field>
            <field name="name">Reference ID {reference_id} is not correct.</field>
            <field name="what_to_do">Make sure the Reference ID does not contain any Arabic characters.</field>
            <field name="error_code">203</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_204" model="rb_delivery.error_log">
            <field name="error_title">You can not edit order</field>
            <field name="name">Can not edit on order {sequence} while the sender {business_name} is deactivated.</field>
            <field name="what_to_do">Go to the sender of order in sequence {sequence} and set the sender {business_name} to active so you can edit this order.</field>
            <field name="error_code">204</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_205" model="rb_delivery.error_log">
            <field name="error_title">You can not edit order</field>
            <field name="name">Orders of sequences {order_sequences} do not have agent assigned, while you're trying to update agent cost.</field>
            <field name="what_to_do">In order to be able to update agent cost, go to orders of sequences {order_sequences} and assign an agent.</field>
            <field name="error_code">205</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_206" model="rb_delivery.error_log">
            <field name="error_title">Error while generating tracking url</field>
            <field name="name">{error_message}</field>
            <field name="what_to_do">Check the error message, and contact the administration.</field>
            <field name="error_code">206</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_207" model="rb_delivery.error_log">
            <field name="error_title">You can not edit / create order</field>
            <field name="name">You can not edit order number {orders_sequences} , picking up time should be larger than current time {current_date}</field>
            <field name="what_to_do">Make sure picking up time is larger than {current_date}.</field>
            <field name="error_code">207</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_208" model="rb_delivery.error_log">
            <field name="error_title">Error while updating Order</field>
            <field name="name">You are trying to update order using reference ID but you are not allowed to overwrite an order while it is in status {order_status}.</field>
            <field name="what_to_do">Check the configuration "allowed_states_to_overwrite_orders", and make sure the order's status is one of the statuses in the configuration. To avoid this issue, you can change the order status to 'Deleted' using the same 'reference id' or use a different 'reference id'.</field>
            <field name="error_code">208</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_209" model="rb_delivery.error_log">
            <field name="error_title">You can not edit order</field>
            <field name="name">The reference ID {reference_id} is used by related Business {business_name} in orders of sequences {order_sequences} and you can not use the same reference twice.</field>
            <field name="what_to_do">You need to choose a different reference ID since you are not allowed to have orders with the same reference ID as one of the related accounts.</field>
            <field name="error_code">209</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_210" model="rb_delivery.error_log">
            <field name="error_title">Error while updating/creating Order</field>
            <field name="name">Sequence is not accepted because of {sequence_issue}.</field>
            <field name="what_to_do">Edit sequence value since it is not accepted by the system.</field>
            <field name="error_code">210</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_211" model="rb_delivery.error_log">
            <field name="error_title">You can not edit order</field>
            <field name="name">Reference ID must be {number_of_digits} digits only.</field>
            <field name="what_to_do">System's configuration allows reference ID to be only {number_of_digits} long, make sure the reference id is applied to this condition.</field>
            <field name="error_code">211</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_212" model="rb_delivery.error_log">
            <field name="error_title">Error while updating/creating Order</field>
            <field name="name">Reference ID must be within {min_digits} and {max_digits} digits.</field>
            <field name="what_to_do">System's configuration allows reference ID to be within {min_digits} and {max_digits} long, make sure the reference id is applied to this condition.</field>
            <field name="error_code">212</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_213" model="rb_delivery.error_log">
            <field name="error_title">Error while updating/creating Order</field>
            <field name="name">{field_name} is not accepted because of {reference_issue}.</field>
            <field name="what_to_do">Edit {field_name} value since it is not accepted by the system.</field>
            <field name="error_code">213</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_214" model="rb_delivery.error_log">
            <field name="error_title">Error while updating country value</field>
            <field name="name">You are trying to update the country with value {customer_country} but it does not exist in the system.</field>
            <field name="what_to_do">Make sure the country you choose exists in the system.</field>
            <field name="error_code">214</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_215" model="rb_delivery.error_log">
            <field name="error_title">Error while creating Order</field>
            <field name="name">You are trying to create the order but do not have a default order type.</field>
            <field name="what_to_do">Go to order type page either create a new order type and set it to default or set an existing one to default.</field>
            <field name="error_code">215</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_247" model="rb_delivery.error_log">
            <field name="error_title">Error while generating money collection</field>
            <field name="name">You do not have orders that statuses in {status_name} OR your orders paid</field>
            <field name="what_to_do">Please make sure that you selected the correct orders and the status is in {status_name}.</field>
            <field name="error_code">247</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_242" model="rb_delivery.error_log">
            <field name="error_title">Error while updating Order</field>
            <field name="name">You are not allowed to change the status to {to_status} directly from the order as its being prevented by the configuration: statues_to_prevent_change_to_if_not_from_agent_collection.</field>
            <field name="what_to_do">{message}</field>
            <field name="error_code">242</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_216" model="rb_delivery.error_log">
            <field name="error_title">Can not update sub area</field>
            <field name="name">Sub area {sub_area} exists but with different {area_count} areas.</field>
            <field name="what_to_do">Make sure you add the area value since the sub area exists with different areas.</field>
            <field name="error_code">216</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_218" model="rb_delivery.error_log">
            <field name="error_title">Can not update sub area</field>
            <field name="name">Sub area {sub_area} exists but with different area {area_name}.</field>
            <field name="what_to_do">Make sure you add the area value {area_name} since the sub area exists with different area.</field>
            <field name="error_code">218</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_219" model="rb_delivery.error_log">
            <field name="error_title">Can not update sub area</field>
            <field name="name">There are two sub areas in sub area map with the same name {sub_area}.</field>
            <field name="what_to_do">Go to the business with name {business_name} and make sure there is only one record in sub area map with the name {sub_area}.</field>
            <field name="error_code">219</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_220" model="rb_delivery.error_log">
            <field name="error_title">Can not update order</field>
            <field name="name">You are trying to update the {field_name} with value {field_value} but the field {field_name} does not exist in the system.</field>
            <field name="what_to_do">Make sure the {field_name} you choose exists in the system.</field>
            <field name="error_code">220</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_221" model="rb_delivery.error_log">
            <field name="error_title">Can not create order</field>
            <field name="name">You are not allowed to create an order as a driver.</field>
            <field name="what_to_do">Contact your adminstration.</field>
            <field name="error_code">221</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_222" model="rb_delivery.error_log">
            <field name="error_title">Error while creating/updating order</field>
            <field name="name">You are trying to update {field_name} value with a user {username} that is not a {role_name}.</field>
            <field name="what_to_do">Choose a {field_name} that its role is {role_name}.</field>
            <field name="error_code">222</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_223" model="rb_delivery.error_log">
            <field name="error_title">Can not create / update order</field>
            <field name="name">You are trying to update 'Reschedule date' value added with {or_equal} to {current_date}.</field>
            <field name="what_to_do">Make sure 'Reschedule date' value is greater than {current_date}.</field>
            <field name="error_code">223</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_224" model="rb_delivery.error_log">
            <field name="error_title">Error while creating/updating order</field>
            <field name="name">Reference ID {reference_id} already exist in order {sequence} for businesses {business_name} and saving will update existing one.</field>
            <field name="what_to_do">Make sure you want to update the order of sequence {sequence} or create new order with different business {business_name}.</field>
            <field name="error_code">224</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_225" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">{field_name} value for order sequence {sequence} should not be empty!</field>
            <field name="what_to_do">Make sure you have a value for {field_name} for order sequence {sequence}.</field>
            <field name="error_code">225</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_226" model="rb_delivery.error_log">
            <field name="error_title">Error while deleting order</field>
            <field name="name">You are not allowed to delete order sequence {sequence} for role {user_role}</field>
            <field name="what_to_do">Please archive the orders or change status to deleted or Contact adminstration.</field>
            <field name="error_code">226</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_227" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">Order of sequence {sequence} has inactive {returned_type} order with sequence {replacement_sequence}</field>
            <field name="what_to_do">Go to orders, filter for Archived orders, search for sequence {replacement_sequence}, select order, then from action choose action Set as deleted, then try again.</field>
            <field name="error_code">227</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_228" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">Order of sequence {sequence} has {returned_type} order with sequence {replacement_sequence}</field>
            <field name="what_to_do">If you need to create a new {returned_type} order go to orders, search for sequence {replacement_sequence}, select order, then from action choose action Set as deleted, then try again.</field>
            <field name="error_code">228</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_229" model="rb_delivery.error_log">
            <field name="error_title">Error while getting location</field>
            <field name="name">There is no location for order of sequence {order_sequence} for customer {order_customer_name}</field>
            <field name="what_to_do">You need to send the url in location url field to the customer so it fills the values needed to get the location.</field>
            <field name="error_code">229</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_230" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">Issue {action_issue} while updating order's status in automated action {action_name}.</field>
            <field name="what_to_do">Check the issue or check with adminstration about the problem.</field>
            <field name="error_code">230</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_231" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">You are not allowed to update order of sequence {order_sequence} status while it is in {collection_type} of sequence {collection_sequence}.</field>
            <field name="what_to_do">Either go to the {collection_type} of sequence {collection_sequence} and change the status, or remove the order from the order of sequence {order_sequence} from {collection_type} of sequence {collection_sequence} before changing status.</field>
            <field name="error_code">231</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_232" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">Due to automated action {action_name} added to the status {status_name} the order of sequence {order_sequence} must be in a {collection_type} in order to change the status {status_name}.</field>
            <field name="what_to_do">Add the order to a {collection_type} before changing to this status {status_name}.</field>
            <field name="error_code">232</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_234" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">The user's role {user_role} has no fields in security file for status {status_name}.</field>
            <field name="what_to_do">Go to security field from Olivery configuration and add a record for the user's role {user_role} with status {status_name}, or contact adminstration.</field>
            <field name="error_code">234</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_235" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">The user's role {user_role} have no access to edit fields {field_names} while the order is in status {status_name} for order sequence {order_sequence}.</field>
            <field name="what_to_do">Go to security field from Olivery configuration and add the fields {field_names} in the record for the user's role with status {status_name}, or contact adminstration.</field>
            <field name="error_code">235</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_236" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">You are not allowed to change order sratus from status  {from_status} to status {status_name} for order sequence {order_sequence}.</field>
            <field name="what_to_do">Make sure the user {user_mobile} of role {user_role} is allowed to change to status {status_name} from status configuration, or contact adminstration.</field>
            <field name="error_code">236</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_237" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">Your role {user_role} have no access to change from status {from_status} to status {status_name} for order of sequence {order_sequence}.</field>
            <field name="what_to_do">Make sure the user {user_mobile} of role {user_role} is allowed to change from status {from_status} to status {status_name}, or the status {status_name} is added as a next status for the status {from_status} from status configuration, or contact adminstration.</field>
            <field name="error_code">237</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_238" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">Unable to parse the date string {date_string} while updating orders through excel.</field>
            <field name="what_to_do">Make sure the date added as a value is in a correct format for a date.</field>
            <field name="error_code">238</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_239" model="rb_delivery.error_log">
            <field name="error_title">Error while chaning the collection status</field>
            <field name="name">To change prepaid collection of sequence {collection_sequence} state to completed or paid, Order state should be {statuses}.</field>
            <field name="what_to_do">Make sure the orders {order_sequences} inside collection status {statuses}.</field>
            <field name="error_code">239</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_240" model="rb_delivery.error_log">
            <field name="error_title">Error while creating collection</field>
            <field name="name">There is no default status for collection to create money collection for order {order_sequence}.</field>
            <field name="what_to_do">Go to olivery configuration then security then collection statuses and select status and set default True.</field>
            <field name="error_code">240</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_241" model="rb_delivery.error_log">
            <field name="error_title">Error while validating customer payment</field>
            <field name="name">Customer Payment must be a numeric value, value entered {customer_payment}.</field>
            <field name="what_to_do">Try again with numeric value.</field>
            <field name="error_code">241</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_243" model="rb_delivery.error_log">
            <field name="error_title">Error changing order states</field>
            <field name="name">You are not allowed to change the state of orders within a prepaid collection unless the desired state for the change in {allowed_status}.</field>
            <field name="what_to_do">Go to the configuration "allowed_states_to_change_pre_paid_order_status" and add the status needed, or contact adminstration</field>
            <field name="error_code">243</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_244" model="rb_delivery.error_log">
            <field name="error_title">Error while creating returned cloned order</field>
            <field name="name">Order of sequence {order} is already exist in returned waybill with sequence {returned_waybill}.</field>
            <field name="what_to_do">Please delete the returned order then try again , from action then set to deleted.</field>
            <field name="error_code">244</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_245" model="rb_delivery.error_log">
            <field name="error_title">Can not create order</field>
            <field name="name">There is no default status in the system and there is no status for the order type you are trying to create order with.</field>
            <field name="what_to_do">To add a default status go to olivery configurations then security then olivery order status then make one of the status default. to add a status for the order type you are trying to use go to settings then configurations then order type then add a status for the order type</field>
            <field name="error_code">245</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_248" model="rb_delivery.error_log">
            <field name="error_title">Error in graph view</field>
            <field name="name">No group by was selected.</field>
            <field name="what_to_do">Please select group by first.</field>
            <field name="error_code">248</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="area_error_250" model="rb_delivery.error_log">
            <field name="error_title">Can not update area</field>
            <field name="name">You are trying to update the {name} with value {area_value} but the name {area_value} exist on another area.</field>
            <field name="what_to_do">Make sure the {name} is unique on the system.</field>
            <field name="error_code">250</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.area</field>
        </record>

        <record id="order_error_251" model="rb_delivery.error_log">
            <field name="error_title">While archiving collection</field>
            <field name="name">You are not allowed to archive collection {collection_type} if u do not have permession .</field>
            <field name="what_to_do">Make sure you have security "collection archiver" from support side  then archive the collection.</field>
            <field name="error_code">251</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_252" model="rb_delivery.error_log">
            <field name="error_title">While creating pre paid collection</field>
            <field name="name">Orders with sequence {sequence} will not be created due to status restrictions, there is no status added .</field>
            <field name="what_to_do">To be able to create pre paid collection order state should be in "ability_to_create_pre_paid_collection" configuration . Go to olivery configuartion then client configuartion then client configuration , search for "ability_to_create_pre_paid_collection" and add order status to it .</field>
            <field name="error_code">252</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_253" model="rb_delivery.error_log">
            <field name="error_title">While creating pre paid collection</field>
            <field name="name">Orders with sequence {sequance} will not be created due to financial state restrictions.</field>
            <field name="what_to_do">To be able to create pre paid collection order financial state should not be paid.</field>
            <field name="error_code">253</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_254" model="rb_delivery.error_log">
            <field name="error_title">While creating paid collection</field>
            <field name="name">Orders with sequence {orders} will not be created due to existing in collection {collection_id}.</field>
            <field name="what_to_do">Remove orders {orders} from collection {collection_id} to add them to a new prepaid collection.</field>
            <field name="error_code">254</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="payment_or_order_type_error_260" model="rb_delivery.error_log">
            <field name="error_title">Error while removing type</field>
            <field name="name">This type {name} is a default you cant remove it.</field>
            <field name="what_to_do">Please select another record as a default type to be able to remove this one.</field>
            <field name="error_code">260</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="pricelist_error_270" model="rb_delivery.error_log">
            <field name="error_title">Error while removing pricelist</field>
            <field name="name">This type {name} You can not delete a pricelist while it is used in users {user_names}</field>
            <field name="what_to_do">Please remove it from users then delete it.</field>
            <field name="error_code">270</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="returned_collection_error_280" model="rb_delivery.error_log">
            <field name="error_title">Error while creating returned collection</field>
            <field name="name">Order {order_sequence} already exists in Returned Collection {collection_sequence}.</field>
            <field name="what_to_do">Please remove it from the collection number {collection_sequence} first.</field>
            <field name="error_code">280</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.returned_money_collection</field>
        </record>

        <record id="returned_collection_error_281" model="rb_delivery.error_log">
            <field name="error_title">Error while creating returned collection</field>
            <field name="name">Order {order_sequence} must have status {order_status}.</field>
            <field name="what_to_do">Please change orders {order_sequence} status to {order_status} hen create returned collection.</field>
            <field name="error_code">281</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.returned_money_collection</field>
        </record>

        <record id="returned_collection_error_282" model="rb_delivery.error_log">
            <field name="error_title">Error while creating returned collection</field>
            <field name="name">Collection will not be created due to Order business should be {order_business}.</field>
            <field name="what_to_do">Please change the business to {order_business}.</field>
            <field name="error_code">282</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.returned_money_collection</field>
        </record>

        <record id="returned_collection_error_283" model="rb_delivery.error_log">
            <field name="error_title">Error while updating returned collection</field>
            <field name="name">You can't edit the collection {collection_sequence}  when the status is Completed Returned.</field>
            <field name="what_to_do">Please make sure that the collection has the correct status.</field>
            <field name="error_code">283</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.returned_money_collection</field>
        </record>

        <record id="returned_collection_error_284" model="rb_delivery.error_log">
            <field name="error_title">Error while creating returned collection</field>
            <field name="name">{message}.</field>
            <field name="what_to_do">Please remove the orders from the collections to able to add them in a new returned collection.</field>
            <field name="error_code">284</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.returned_money_collection</field>
        </record>

        <record id="returned_collection_error_285" model="rb_delivery.error_log">
            <field name="error_title">Error while Creating returned collection</field>
            <field name="name">Order {sequence} has no business added to it.</field>
            <field name="what_to_do">Please add a business to the order {sequence}.</field>
            <field name="error_code">285</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.returned_money_collection</field>
        </record>

        <record id="returned_collection_error_286" model="rb_delivery.error_log">
            <field name="error_title">Error Creating collection</field>
            <field name="name">To create returened collection ,order status should be added  to the  configuration "returned_collection_status".</field>
            <field name="what_to_do">Go to olivery configuaration then client configuration then client configuration then search for "returned_collection_status" add order status to it.</field>
            <field name="error_code">286</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.returned_money_collection</field>
        </record>

        <record id="returned_collection_error_287" model="rb_delivery.error_log">
            <field name="error_title">Error Creating collection</field>
            <field name="name">To create returned collection, order {order_sequence} status must be in {message}.</field>
            <field name="what_to_do">Please change the order status to {message}.</field>
            <field name="error_code">287</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.returned_money_collection</field>
        </record>

        <record id="runsheet_error_290" model="rb_delivery.error_log">
            <field name="error_title">Error Creating runsheet collection</field>
            <field name="name">There is no agent in these orders {messages}.</field>
            <field name="what_to_do">Please add an agent to orders {messages} to create runsheet collection .</field>
            <field name="error_code">290</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.runsheet</field>
        </record>

        <record id="create_runsheet_error_291" model="rb_delivery.error_log">
            <field name="error_title">Error Creating runsheet collection</field>
            <field name="name">{exist_message}.</field>
            <field name="what_to_do">Please remove the orders from the agent collection first.</field>
            <field name="error_code">291</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.create_runsheet</field>
        </record>

        <record id="runsheet_error_292" model="rb_delivery.error_log">
            <field name="error_title">Error while changing status</field>
            <field name="name">You are not allowed to change from {first} to {second} for {collection_type}.</field>
            <field name="what_to_do">Make sure that status you changed to {first}  is next status to {second} then try to change {collection_type} status .</field>
            <field name="error_code">292</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.runsheet</field>
        </record>

        <record id="runsheet_error_293" model="rb_delivery.error_log">
            <field name="error_title">Error while changing status</field>
            <field name="name">Your group is not allowed to change to this status {status} for {collection_type}.</field>
            <field name="what_to_do">Make sure that you added to who can change to this status for status {status} for {collection_type} , Go to olivery configuration then security then collection statuses then go to status {status} then then add your role to who can change to this status .</field>
            <field name="error_code">293</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.runsheet</field>
        </record>

        <record id="update_cost_error_294" model="rb_delivery.error_log">
            <field name="error_title">Error Update COD values</field>
            <field name="name">You can not enter a value less than 0 for {field_name}.</field>
            <field name="what_to_do">Make sure "accept_minus_value_in_cod" is checked true to accept minus value , go to olivery configuration then client configuration then client configuration then search for "accept_minus_value_in_cod" then check value for true .</field>
            <field name="error_code">294</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_295" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">Orders of sequences {order_sequences} are archived, you can not change its status.</field>
            <field name="what_to_do">Go to orders of sequences {order_sequences} then to action choose Unarchive order then try to change status again.</field>
            <field name="error_code">295</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_296" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">Order of reference {order_reference} already exist in active orders, you can't change its status.</field>
            <field name="what_to_do">Go to order of reference {order_reference} of status {active_status} or with status {inactive_status} and change the reference ID of one of the orders to something else before trying to change the order of sequence {order_sequence} status.</field>
            <field name="error_code">296</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="update_cost_error_297" model="rb_delivery.error_log">
            <field name="error_title">Error While Updating Order's Area</field>
            <field name="name">The area {area_name} chosen is not active.</field>
            <field name="what_to_do">Go to areas, search in inactive areas for area {area_name} then make it active. Or you can choose a different area.</field>
            <field name="error_code">297</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_298" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">You can not change status since you have choosen two orders that are inactive and have the same reference ID {order_reference}.</field>
            <field name="what_to_do">Go to all orders then search for reference {order_reference} and change the reference ID for one of them before trying to change the status to an active status again.</field>
            <field name="error_code">298</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_301" model="rb_delivery.error_log">
            <field name="error_title">Error while creating agent collection</field>
            <field name="name">Order {order_sequence} already exists in Agent Collection {collection_sequence}</field>
            <field name="what_to_do">Try removing it from the collection with the sequence number {collection_sequence} then create agent collection for this orders .</field>
            <field name="error_code">301</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_302" model="rb_delivery.error_log">
            <field name="error_title">Error while creating agent collection</field>
            <field name="name">To create agent collection Order {order_sequence} state should be {order_status_title}</field>
            <field name="what_to_do">Try changing the order {order_sequence} status to: {order_status_title} then make agent collection for this order.</field>
            <field name="error_code">302</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_303" model="rb_delivery.error_log">
            <field name="error_title">Error while creating agent collection</field>
            <field name="name">Order {order_sequence} agent should be {order_agent} to create agent collection </field>
            <field name="what_to_do">Try to change  the agent to: {order_agent} for the orders {order_sequence}.</field>
            <field name="error_code">303</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_305" model="rb_delivery.error_log">
            <field name="error_title">Error Updating Collection</field>
            <field name="name">You can't edit the agent collection of sequecne {collection_sequence} details since it is a completed collection.</field>
            <field name="what_to_do">You can go to the agent collection of sequence {collection_sequence} and change its status before being able to edit the agent collection details.</field>
            <field name="error_code">305</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_306" model="rb_delivery.error_log">
            <field name="error_title">Error Whlie removing order from agent collection</field>
            <field name="name">This Order {order_sequance} is not allowed to be removed from the agent collection since it is in {order_status} status.</field>
            <field name="what_to_do">Change the order status to be able to remove it</field>
            <field name="error_code">306</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_307" model="rb_delivery.error_log"> <!-- To be deprecated  -->
            <field name="error_title">Error Creating agent collection</field>
            <field name="name">{exist_message}.</field>
            <field name="what_to_do">Please remove the order from these collections then you can create a new agent collection with the.</field>
            <field name="error_code">307</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_308" model="rb_delivery.error_log">
            <field name="error_title">Error Creating agent collection</field>
            <field name="name">There is no agent in these orders {exist_message}.</field>
            <field name="what_to_do">Please add a agent  to the orders then create agent collection .</field>
            <field name="error_code">308</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_309" model="rb_delivery.error_log"> <!-- To be deprecated (not sure)  -->
            <field name="error_title">Error Creating collection</field>
            <field name="name">The order with sequence {order_sequence} has no business.</field>
            <field name="what_to_do">Please add a business to the order.</field>
            <field name="error_code">309</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_310" model="rb_delivery.error_log">
            <field name="error_title">Error Creating collection</field>
            <field name="name">The order with sequance {order_sequence} has no agent.</field>
            <field name="what_to_do">Please contact your adminstrator to add states for agent collection.</field>
            <field name="error_code">310</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_311" model="rb_delivery.error_log">
            <field name="error_title">Error Creating collection</field>
            <field name="name">You have no orders that are not in a collection.</field>
            <field name="what_to_do">Please check if you are trying to do the correct action.</field>
            <field name="error_code">311</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_312" model="rb_delivery.error_log">
            <field name="error_title">Error while creating collection</field>
            <field name="name">Order agent should be {order_agent}</field>
            <field name="what_to_do">Try changing the agent to: {order_agent}.</field>
            <field name="error_code">312</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_313" model="rb_delivery.error_log">
            <field name="error_title">Error while creating collection</field>
            <field name="name">You have some unresolved orders with status delivered stuck</field>
            <field name="what_to_do">please contact you acountent to resolve them.</field>
            <field name="error_code">313</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_money_collection</field>
        </record>

        <record id="order_error_320" model="rb_delivery.error_log">
            <field name="error_title">Error while creating collection</field>
            <field name="name">Order with sequance number: {order_sequance} already exists in collection {collection_sequence}</field>
            <field name="what_to_do">Either remove from the collection number {collection_sequence} or dont add it to the collection you are trying to create.</field>
            <field name="error_code">320</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_returned_collection</field>
        </record>

        <record id="order_error_321" model="rb_delivery.error_log">
            <field name="error_title">Error while creating collection</field>
            <field name="name">Order with the sequance {order_sequance} status must be {order_status} to create returned agent collection</field>
            <field name="what_to_do">Change the order status to {order_status}.</field>
            <field name="error_code">321</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_returned_collection</field>
        </record>

        <record id="order_error_323" model="rb_delivery.error_log">
            <field name="error_title">Error while creating collection</field>
            <field name="name">Order with the sequance {order_sequance} must have agent</field>
            <field name="what_to_do">Please add agent to the order with sequance {order_sequance}.</field>
            <field name="error_code">323</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_returned_collection</field>
        </record>

        <record id="order_error_324" model="rb_delivery.error_log">
            <field name="error_title">Error while creating collection</field>
            <field name="name">Order with the sequance {order_sequance} must have agent {driver_name}</field>
            <field name="what_to_do">Please add agent {driver_name} to the order with sequance {order_sequance}.</field>
            <field name="error_code">324</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_returned_collection</field>
        </record>

        <record id="order_error_325" model="rb_delivery.error_log">
            <field name="error_title">Error while updating collection</field>
            <field name="name">You can't edit the collection when the status is Completed Returned</field>
            <field name="what_to_do">Please check if the collection has the correct status.</field>
            <field name="error_code">325</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_returned_collection</field>
        </record>

        <record id="order_error_326" model="rb_delivery.error_log">
            <field name="error_title">Error while creaeting collection</field>
            <field name="name">These orders has no agent: {orders_no_agent}</field>
            <field name="what_to_do">Please add agent to the orders.</field>
            <field name="error_code">326</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_returned_collection</field>
        </record>

        <record id="order_error_327" model="rb_delivery.error_log">
            <field name="error_title">Error while creaeting collection</field>
            <field name="name">Order with the sequance {order_sequance} has no business</field>
            <field name="what_to_do">Please add business to the order {order_sequance}.</field>
            <field name="error_code">327</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_returned_collection</field>
        </record>

        <record id="collection_error_328" model="rb_delivery.error_log">
            <field name="error_title">Error while creating agent returned  collection</field>
            <field name="name">There is no statuses in the configuration for returned agent collection named 'agent_returned_collection_status'.</field>
            <field name="what_to_do">Please  add states for collection configuration 'agent_returned_collection_status' , go to olivery configuration then client configuration then client configuration then search for "agent_returned_collection_status" then add status .</field>
            <field name="error_code">328</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_returned_collection</field>
        </record>

        <record id="collection_error_329" model="rb_delivery.error_log">
            <field name="error_title">Error while creating agent returned collection</field>
            <field name="name">To create agent returned collection, orders {order_sequance} status must be in {message} instead of  status {order_status}.</field>
            <field name="what_to_do">Please change the order {order_sequance} status to {message} then create agent returned collection .</field>
            <field name="error_code">329</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.agent_returned_collection</field>
        </record>

        <record id="configuration_error_400" model="rb_delivery.error_log">
            <field name="error_title">Error while update orders</field>
            <field name="name">There is no selected orders to change status to the orders .</field>
            <field name="what_to_do">Please choose orders then change status.</field>
            <field name="error_code">400</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.client_configuration</field>
        </record>

        <record id="configuration_error_401" model="rb_delivery.error_log">
            <field name="error_title">Error while sending orders</field>
            <field name="name">User {delivery_company} does not have company url, company password and company username.</field>
            <field name="what_to_do">Please add  company url, company password and company to the user{delivery_company} in the vhub section in user page to be able to send orders.</field>
            <field name="error_code">401</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.client_configuration</field>
        </record>

        <record id="configuration_error_402" model="rb_delivery.error_log">
            <field name="error_title">Error while sending orders</field>
            <field name="name">User must be driver and company user.</field>
            <field name="what_to_do">Please try choosing another user or make this user a driver.</field>
            <field name="error_code">402</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.client_configuration</field>
        </record>

        <record id="configuration_error_403" model="rb_delivery.error_log">
            <field name="error_title">Error while sending orders</field>
            <field name="name">You didn't choose a user or order.</field>
            <field name="what_to_do">Please add a user and an order.</field>
            <field name="error_code">403</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.client_configuration</field>
        </record>

        <record id="configuration_error_405" model="rb_delivery.error_log">
            <field name="error_title">Error while sending orders</field>
            <field name="name">The '_' symbol cannot be used in 'text' when key is 'clone_order_postfix' or 'clone_order_prefix.</field>
            <field name="what_to_do">Make sure to add valid symbols to the configuration  'clone_order_postfix' or 'clone_order_prefix'.</field>
            <field name="error_code">405</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.client_configuration</field>
        </record>

        <record id="location_error_410" model="rb_delivery.error_log">
            <field name="error_title">Error while trying to get the location</field>
            <field name="name">There is no location for user {user}.</field>
            <field name="what_to_do">Please add location.</field>
            <field name="error_code">410</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.client_configuration</field>
        </record>

        <record id="notificaion_error_420" model="rb_delivery.error_log">
            <field name="error_title">Error while trying to send notification</field>
            <field name="name">company dose not have an email.</field>
            <field name="what_to_do">Please add an email to your company.</field>
            <field name="error_code">420</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.client_configuration</field>
        </record>

        <record id="service_error_430" model="rb_delivery.error_log">
            <field name="error_title">Error while changing/creating service</field>
            <field name="name">Wrong serive type.</field>
            <field name="what_to_do">You should select service type to be in sender or customer.</field>
            <field name="error_code">430</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.service</field>
        </record>

        <record id="service_error_431" model="rb_delivery.error_log">
            <field name="error_title">Error while removing/unlinking service</field>
            <field name="name">This service {name} is included in the order {order_sequence}</field>
            <field name="what_to_do">Please remove it from the order then try again.</field>
            <field name="error_code">431</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.service</field>
        </record>

        <record id="sms_error_440" model="rb_delivery.error_log">
            <field name="error_title">Error while sending SMS</field>
            <field name="name">No SID was found in your twilio information.</field>
            <field name="what_to_do">Please add an SID to your twilio information.</field>
            <field name="error_code">440</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.sms</field>
        </record>

        <record id="sms_error_441" model="rb_delivery.error_log">
            <field name="error_title">Error while sending SMS</field>
            <field name="name">No auth token was found in your twilio information.</field>
            <field name="what_to_do">Please contact support to add settings to sms.</field>
            <field name="error_code">441</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.sms</field>
        </record>

        <record id="sms_error_442" model="rb_delivery.error_log">
            <field name="error_title">Error while sending SMS</field>
            <field name="name">No Messaging Service SID was found in your twilio information.</field>
            <field name="what_to_do">Please contact support to add settings to sms.</field>
            <field name="error_code">442</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.sms</field>
        </record>

        <record id="sms_error_443" model="rb_delivery.error_log">
            <field name="error_title">Error while sending SMS</field>
            <field name="name">No SMS information was found.</field>
            <field name="what_to_do">please add your sms information.</field>
            <field name="error_code">443</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.sms</field>
        </record>

        <record id="status_error_450" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't change the status name or deactivate status while it's used in orders {order_sequence}.</field>
            <field name="what_to_do">Wait for the order to be completed to change status name or deactivate it .</field>
            <field name="error_code">450</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_451" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't change the status name or deactivate status while it's used in returned collections.</field>
            <field name="what_to_do">Wait for the order to be completed then try to change status name or deactivate it .</field>
            <field name="error_code">451</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_452" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't change the status name or deactivate status {status} while it's used in collections {collection_sequence}.</field>
            <field name="what_to_do">Wait for the order to be completed to change status name or deactivate it .</field>
            <field name="error_code">452</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_453" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't change the status name or deactivate status while it's used in agent collections.</field>
            <field name="what_to_do">Wait for the order to be completed to change status name or deactivate it .</field>
            <field name="error_code">453</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_454" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't change the status name or deactivate status while it's used in agent returned collections {collection_sequence}.</field>
            <field name="what_to_do">Wait for the order to be completed to change status name or deactivate it .</field>
            <field name="error_code">454</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_455" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't delete the status while it's used in orders {order_sequence} for status {name}.</field>
            <field name="what_to_do">Wait for the order to be completed then delete it.</field>
            <field name="error_code">455</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_456" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't delete the status while it's used in returned collections {collection_sequence}.</field>
            <field name="what_to_do">Wait for the order to be completed then delete it.</field>
            <field name="error_code">456</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_457" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't delete the status while it's used in collections {collection_sequence}.</field>
            <field name="what_to_do">Wait for the order to be completed.</field>
            <field name="error_code">457</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_458" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't delete the status while it's used in agent collections {collection_sequence}</field>
            <field name="what_to_do">Wait for the order to be completed then delete it.</field>
            <field name="error_code">458</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_459" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't delete the status while it's used in agent returned collections {collection_sequence}</field>
            <field name="what_to_do">Wait for the order to be completed then delete it.</field>
            <field name="error_code">459</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_460" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">This role {user_role} has no access to this status {status}</field>
            <field name="what_to_do">You should go to olivery configuration then security then olivery order status then search for {status} then add role {user_role} to "who can access status".</field>
            <field name="error_code">460</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_461" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">Roles {user_role} to show in segments for this status {status} must have access to it.</field>
            <field name="what_to_do">You should go to olivery configuration then security then olivery order status then search for {status} then add role {user_role} to "who can access status".</field>
            <field name="error_code">461</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="status_error_462" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">Status with name {status_name} already exists.</field>
            <field name="what_to_do">You should change the status name.</field>
            <field name="error_code">462</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>

        <record id="sub_area_error_470" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">Sub area of name {area_name} already exists with code {area_code}.</field>
            <field name="what_to_do">You can not add two sub areas with the same name and same area.</field>
            <field name="error_code">470</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.sub_area</field>
        </record>

        <record id="user_error_499" model="rb_delivery.error_log">
            <field name="error_title">Error while getting default order for user</field>
            <field name="name">order type is empty for the user {user_name}.</field>
            <field name="what_to_do">Please Add the default order for the user.</field>
            <field name="error_code">499</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_500" model="rb_delivery.error_log">
            <field name="error_title">Error while creating user</field>
            <field name="name">you are not allowed to create user since Wrong mobile number of digits , the number of digits must be {min_digits} less.</field>
            <field name="what_to_do">Please modify the mobile number to be at least the minimum number of allowed digit {min_digits}.</field>
            <field name="error_code">500</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_501" model="rb_delivery.error_log">
            <field name="error_title">Error while creating user</field>
            <field name="name">you are not allowed to create user since Wrong mobile number of digits , the number of digits must be between  {min_digits} and {max_digit}</field>
            <field name="what_to_do">Please modify the mobile number to be between {min_digits} and {max_digit}.</field>
            <field name="error_code">501</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_502" model="rb_delivery.error_log">
            <field name="error_title">Error while creating user</field>
            <field name="name">you are not allowed to create user since Password less than  8 characters</field>
            <field name="what_to_do">Please try another password with at least 8 characters .</field>
            <field name="error_code">502</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_503" model="rb_delivery.error_log">
            <field name="error_title">Error while creating user</field>
            <field name="name">Name {name} is duplicated in area map list, the name should be unique</field>
            <field name="what_to_do">Please try another sub area name, or choose area then sub area.</field>
            <field name="error_code">503</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_504" model="rb_delivery.error_log">
            <field name="error_title">Error while creating user</field>
            <field name="name">You are not allowed to create user if Area {name} does not exist</field>
            <field name="what_to_do">Either create new area or check if you have any misspelling.</field>
            <field name="error_code">504</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_505" model="rb_delivery.error_log">
            <field name="error_title">Error while creating user</field>
            <field name="name">You are not allowed to create user if Email {email} already exist</field>
            <field name="what_to_do">Please choose another email address.</field>
            <field name="error_code">505</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_506" model="rb_delivery.error_log">
            <field name="error_title">Error while creating user</field>
            <field name="name">You do not have access to create new user with this role {new_user_role}</field>
            <field name="what_to_do">Please contact any super manager or manager to create a user.</field>
            <field name="error_code">506</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_507" model="rb_delivery.error_log">
            <field name="error_title">Error while creating user</field>
            <field name="name">You are not allowed to create user if Mobile {mobile_number} already exists</field>
            <field name="what_to_do">Please choose another mobile number.</field>
            <field name="error_code">507</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_508" model="rb_delivery.error_log">
            <field name="error_title">Error while creating user</field>
            <field name="name">This user {user_name} was archived on {archive_date} by {archiving_user} </field>
            <field name="what_to_do">Make sure to choose different mobile number , or unarchive exist user.</field>
            <field name="error_code">508</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_511" model="rb_delivery.error_log">
            <field name="error_title">Error while trying to update user</field>
            <field name="name">This user {user} can't be Deactivated.</field>
            <field name="what_to_do">The user is being used for app store please do not activate you the user.</field>
            <field name="error_code">511</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_512" model="rb_delivery.error_log">
            <field name="error_title">Error while trying to update user</field>
            <field name="name">You are not allowed to edit this user {user} since there is orders not completed.</field>
            <field name="what_to_do">Please make sure that all orders for the user done (completed)  then change the user info.</field>
            <field name="error_code">512</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_513" model="rb_delivery.error_log">
            <field name="error_title">Error while creating a user</field>
            <field name="name">The email {email} cant have arabic characters.</field>
            <field name="what_to_do">Please type another email without any arabic characters.</field>
            <field name="error_code">513</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_514" model="rb_delivery.error_log">
            <field name="error_title">Error while creating a user</field>
            <field name="name">The email {email} can only contain ASCII characters.</field>
            <field name="what_to_do">Please type another email with only ASCII characters.</field>
            <field name="error_code">514</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_515" model="rb_delivery.error_log">
            <field name="error_title">Error while deactivating a user</field>
            <field name="name">You Cannot deactivate {user} since the user has uncompleted orders.</field>
            <field name="what_to_do">Go to orders and filter on this business name {user}, finish process and make sure all orders are processed before deactivating.</field>
            <field name="error_code">515</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_516" model="rb_delivery.error_log"> <!-- USED IN API -->
            <field name="error_title">Error while changing password</field>
            <field name="name">You are not allowed to change the password for this user. Only Super Managers can change passwords for this role. You are only permitted to change passwords for business users and drivers.</field>
            <field name="what_to_do">Please contact a Super Manager to request a password change for this user.</field>
            <field name="error_code">516</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_517" model="rb_delivery.error_log">
            <field name="error_title">Error while getting location</field>
            <field name="name">There is no location.</field>
            <field name="what_to_do">Please provide correct lat and log.</field>
            <field name="error_code">517</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_518" model="rb_delivery.error_log"> <!-- FOR MOBILE -->
            <field name="error_title">Error while registaring mobile</field>
            <field name="name">{error}.</field>
            <field name="what_to_do">Please contact administration , go to olivery configuration then client configuration then client configuration then enable login_enable_without_confirmation.</field>
            <field name="error_code">518</field>
            <field name="error_type">mobile</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_519" model="rb_delivery.error_log">
            <field name="error_title">Error while deleting user</field>
            <field name="name">this user {user} cant be deleted.</field>
            <field name="what_to_do">this user is being used in the app.</field>
            <field name="error_code">519</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_520" model="rb_delivery.error_log">
            <field name="error_title">Error while changing password</field>
            <field name="name">You are not allowed to change Admin's password.</field>
            <field name="what_to_do">Please contact your administration.</field>
            <field name="error_code">520</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_530" model="rb_delivery.error_log">
            <field name="error_title">Error while archiving user</field>
            <field name="name">You cant archive yourself.</field>
            <field name="what_to_do">Please contact adminstrator.</field>
            <field name="error_code">530</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_531" model="rb_delivery.error_log">
            <field name="error_title">Error while checking pricelist</field>
            <field name="name">pricelist {pricelist} dose not exist in the system.</field>
            <field name="what_to_do">Please check the spelling again or make sure that you are looking for the correct pricelist.</field>
            <field name="error_code">531</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_532" model="rb_delivery.error_log">
            <field name="error_title">Error while checking sub area</field>
            <field name="name">Sub area {area_sub} dose not exist in the system.</field>
            <field name="what_to_do">Please check the spelling again or make sure that you are looking for the correct sub area.</field>
            <field name="error_code">532</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_533" model="rb_delivery.error_log">
            <field name="error_title">Error while creating a user</field>
            <field name="name">No area was provided.</field>
            <field name="what_to_do">Please provide an area to be able to create a user.</field>
            <field name="error_code">533</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="utility_error_550" model="rb_delivery.error_log">
            <field name="error_title">Error while Print report</field>
            <field name="name">{error}</field>
            <field name="what_to_do">Please contact adminstrator to fix this issue</field>
            <field name="error_code">550</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.utility</field>
        </record>

        <record id="utility_error_551" model="rb_delivery.error_log">
            <field name="error_title">Error while checking mobile number</field>
            <field name="name">Your {field_name} must contain only numbers</field>
            <field name="what_to_do">Please try another mobile number with only digis</field>
            <field name="error_code">551</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.utility</field>
        </record>

        <record id="utility_error_552" model="rb_delivery.error_log">
            <field name="error_title">Error while checking {field_name}</field>
            <field name="name">{field_name} must be {min_digit} digits only</field>
            <field name="what_to_do">Please make the {field_name} only {min_digit} digits</field>
            <field name="error_code">552</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.utility</field>
        </record>

        <record id="utility_error_553" model="rb_delivery.error_log">
            <field name="error_title">Error while checking {field_name}</field>
            <field name="name">{field_name} must be between {min_digit} digits and {max_digit}</field>
            <field name="what_to_do">Please make the {field_name} between {min_digit} digits and {max_digit}</field>
            <field name="error_code">553</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.utility</field>
        </record>

        <record id="select_from_to_area_error_560" model="rb_delivery.error_log">
            <field name="error_title">Error while selecting area</field>
            <field name="name">Invalid from/to area</field>
            <field name="what_to_do">please select from/to area</field>
            <field name="error_code">560</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.select_from_to_area</field>
        </record>

        <record id="select_from_to_area_error_561" model="rb_delivery.error_log">
            <field name="error_title">Error while selecting area</field>
            <field name="name">No area was selected when go to pricelist item then then choose one then action then select area </field>
            <field name="what_to_do">Please select area first</field>
            <field name="error_code">561</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.select_from_to_area</field>
        </record>

        <record id="mobile_form_creator_error_570" model="rb_delivery.error_log">
            <field name="error_title">Error in mobile form creator</field>
            <field name="name">A form with the same role and status {group_id} already exists: {field_name}</field>
            <field name="what_to_do">Please choose different form creator id</field>
            <field name="error_code">570</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.mobile_form_creator</field>
        </record>

        <record id="mobile_form_creator_error_580" model="rb_delivery.error_log">
            <field name="error_title">Error in adding items</field>
            <field name="name">You can add maximum 3 items!</field>
            <field name="what_to_do">Please add only 3 items</field>
            <field name="error_code">580</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.mobile_card_item</field>
        </record>

        <record id="user_duplication_error_581" model="rb_delivery.error_log">
            <field name="error_title">Error in duplicating the user</field>
            <field name="name">You are not allowed to duplicate users</field>
            <field name="what_to_do">Please ask your super-manger to duplicate this user</field>
            <field name="error_code">581</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="order_draft_error_600" model="rb_delivery.error_log">
            <field name="error_title">Error while preparing data</field>
            <field name="name">There are more than one user with the same information {record}</field>
            <field name="what_to_do">Please make sure that the sender for the data with correct value or choose mobile number of sender instead name </field>
            <field name="error_code">600</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order_draft</field>
        </record>

        <record id="order_draft_error_601" model="rb_delivery.error_log">
            <field name="error_title">Error while preparing data</field>
            <field name="name">User with the name {record} was not found</field>
            <field name="what_to_do">Please make sure that the sender for the data with correct value</field>
            <field name="error_code">601</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order_draft</field>
        </record>

        <record id="order_next_status_error_603" model="rb_delivery.error_log">
            <field name="error_title">Error while preparing data</field>
            <field name="name">You select orders with status {status_name} and you do not have access to change to next statuses for status {status_name}</field>
            <field name="what_to_do">You should have access to  change to  next status for the status {status_name} . Go to olivery configuration then security then olivery order status then statuses {status_name} then add user role to role who can access and role who can change to next statuses for {status_name} or contact support</field>
            <field name="error_code">603</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.select_state</field>
        </record>

        <record id="general_configuration_error_610" model="rb_delivery.error_log">
            <field name="error_title">Error while checking url</field>
            <field name="name">Invalid URL {url} Link</field>
            <field name="what_to_do">Please provid a valid  link support url in general configuration</field>
            <field name="error_code">610</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.general_configuration</field>
        </record>

        <record id="general_configuration_error_611" model="rb_delivery.error_log">
            <field name="error_title">Error while checking phone</field>
            <field name="name">Invalid Phone Number {phone}</field>
            <field name="what_to_do">Please provid a valid phone number</field>
            <field name="error_code">611</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.general_configuration</field>
        </record>

        <record id="general_configuration_error_612" model="rb_delivery.error_log">
            <field name="error_title">Error while checking email</field>
            <field name="name">Invalid email address {email}</field>
            <field name="what_to_do">Please provide a valid emaill address</field>
            <field name="error_code">612</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.general_configuration</field>
        </record>

        <record id="security_matrix_error_620" model="rb_delivery.error_log">
            <field name="error_title">Cannot deny access for default report</field>
            <field name="name">This report is set as default to print on mobile and cannot be deactivated!</field>
            <field name="what_to_do">Please choose another report in mobile default print menu then try again.</field>
            <field name="error_code">620</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.general_configuration</field>
        </record>

        <record id="status_error_650" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't change the status for {number_of_orders} orders that contains a delivery cost equal to zero and the customer area is the default area, for example order sequences: {zero_cost_orders}</field>
            <field name="what_to_do">Please make sure that orders you selected with customer area: {customer_area} have pricing with areas that exist in the system. Or choose another value for customer area field different than you chose, or please contact support team.</field>
            <field name="error_code">650</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.select_state</field>
        </record>

        <record id="follow_up_order_error_700" model="rb_delivery.error_log">
            <field name="error_title">Error while checking follow order</field>
            <field name="name">Follow order {name} with the sequence {sequence} already been used for another order</field>
            <field name="what_to_do">Please use another sequecne for the follow order {name}</field>
            <field name="error_code">700</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.follow_up_order</field>
        </record>

        <record id="follow_up_order_error_701" model="rb_delivery.error_log">
            <field name="error_title">Error while checking follow order</field>
            <field name="name">Follow order {name} with the sequence {sequence} already been used for another follow order</field>
            <field name="what_to_do">Please use another sequecne for the follow order {name}</field>
            <field name="error_code">701</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.follow_up_order</field>
        </record>

        <record id="user_error_543" model="rb_delivery.error_log">
            <field name="error_title">Error while creating a user</field>
            <field name="name">No mobile number was provided.</field>
            <field name="what_to_do">Please provide a mobile number to be able to create a user.</field>
            <field name="error_code">543</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_544" model="rb_delivery.error_log">
            <field name="error_title">Error while creating a user</field>
            <field name="name">No user name was provided.</field>
            <field name="what_to_do">Please provide a user name to be able to create a user.</field>
            <field name="error_code">544</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_545" model="rb_delivery.error_log">
            <field name="error_title">Error while creating a user</field>
            <field name="name">No address was provided.</field>
            <field name="what_to_do">Please provide an address to be able to create a user.</field>
            <field name="error_code">545</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_249" model="rb_delivery.error_log">
            <field name="error_title">Error while creating order</field>
            <field name="name">Error while creaing order, values are invalid</field>
            <field name="what_to_do">Please contact support.</field>
            <field name="error_code">249</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_246" model="rb_delivery.error_log">
            <field name="error_title">Error while creating order</field>
            <field name="name">No values to create order</field>
            <field name="what_to_do">Please contact support.</field>
            <field name="error_code">246</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_255" model="rb_delivery.error_log">
            <field name="error_title">Error while Picking order</field>
            <field name="name">User was not found in the system/ or the user trying to pickup the order is not a driver.</field>
            <field name="what_to_do">Please check your roles.</field>
            <field name="error_code">255</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_256" model="rb_delivery.error_log">
            <field name="error_title">Error while Picking order</field>
            <field name="name">Order with sequence / reference_id {sequence} was not found</field>
            <field name="what_to_do">Please check the sequence / reference id again or try scanning another order.</field>
            <field name="error_code">256</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_257" model="rb_delivery.error_log">
            <field name="error_title">Error while Picking order</field>
            <field name="name">No default status for picked up orders was found</field>
            <field name="what_to_do">Please go to olivery configuaration and add a status to picked_up_status configuaration.</field>
            <field name="error_code">257</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_257" model="rb_delivery.error_log">
            <field name="error_title">Error while Picking order</field>
            <field name="name">No default status for picked up orders was found</field>
            <field name="what_to_do">Please go to olivery configuaration and add a status to picked_up_status configuaration.</field>
            <field name="error_code">257</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="order_error_276" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">You do not have access to change orders with sequence {sequences}</field>
            <field name="what_to_do">Ask from Administrator to allow you to do that change</field>
            <field name="error_code">276</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>
        <record id="order_error_337" model="rb_delivery.error_log">
            <field name="error_title">Error while creating order</field>
            <field name="name">You can't add the order because there is no pricelist from sender area {from} to receiver area {to}</field>
            <field name="what_to_do">Please go to pricelist {price_list_name} and make sure you have pricing between area {from} and {to}</field>
            <field name="error_code">337</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>
        <record id="order_error_338" model="rb_delivery.error_log">
            <field name="error_title">Error while editing order</field>
            <field name="name">{user_role} can't edit the order of sequence {sequence} in status {status}</field>
            <field name="what_to_do">Please go to olivery configuaration and remove your role from Role can not edit orders in this status section or Ask from Administrator to allow you to do that change</field>
            <field name="error_code">338</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>
        <record id="status_error_339" model="rb_delivery.error_log">
            <field name="error_title">Error while updating status</field>
            <field name="name">You can't change the status name or deactivate status while it's used in orders type {orders_type}.</field>
            <field name="what_to_do">You have to change default order type status to another status.Please go to setting then configuration then order type then select order type appeared in message and then change Related Order State field</field>
            <field name="error_code">339</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.status</field>
        </record>
        <record id="nav_items_error_340" model="rb_delivery.error_log">
            <field name="error_title">Error while updating nav items</field>
            <field name="name">You can't set more than 5 navigation items.</field>
            <field name="what_to_do">You have to change the navigation items to include maximum {nav_items_length} navigation items</field>
            <field name="error_code">340</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.navigation_creator</field>
        </record>

        <record id="user_error_546" model="rb_delivery.error_log">
            <field name="error_title">Error while creating / editing address tag</field>
            <field name="name">There is already an address tag with the same area and sub area.</field>
            <field name="what_to_do">Please provide (area/sub area) that is not exist in previously created address tags.</field>
            <field name="error_code">546</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>

        <record id="user_error_547" model="rb_delivery.error_log">
            <field name="error_title">Error refreshing full address for the orders</field>
            <field name="name">There is orders that doesn't need to be refreshed.</field>
            <field name="what_to_do">Please only choose orders that their full address values has been changed (you can use the filter 'Full Address Values Changed' inside filters list).</field>
            <field name="error_code">547</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.user</field>
        </record>


        <record id="order_error_900" model="rb_delivery.error_log">
            <field name="error_title">OneSignal Connection Error</field>
            <field name="name">Failed to connect to OneSignal API</field>
            <field name="what_to_do">Please check the App ID and Authentication Key, and ensure that the OneSignal service is reachable.</field>
            <field name="error_code">900</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.one_signal</field>
        </record>

        <record id="order_error_801" model="rb_delivery.error_log">
            <field name="error_title">Error while updating Sort and destribute</field>
            <field name="name">There is already a setting item with the same from status for the same sort and destribute setting package.</field>
            <field name="what_to_do">Please go to the other setting item and change the from status or change this one.</field>
            <field name="error_code">801</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>


        <record id="order_error_802" model="rb_delivery.error_log">
            <field name="error_title">Can't update order</field>
            <field name="name">No area found while getting locality by coordinates.</field>
            <field name="what_to_do">Please try another coordinates (Latitude,Longitude).</field>
            <field name="error_code">802</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_803" model="rb_delivery.error_log">
            <field name="error_title">Can't update order</field>
            <field name="name">Failed to get response while getting locality by (Latitude,Longitude)</field>
            <field name="what_to_do">Please make sure you have the correct api key set on your system or contact support.</field>
            <field name="error_code">803</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_804" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name"> You can not create a replacement order for the order of sequence {order_sequences} .</field>
            <field name="what_to_do">If you need to make order replacement Go to olivery configuration  then client configuration then client configuration search for configuration replacement_order_status then add the status {status} to the configuration.</field>
            <field name="error_code">804</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_805" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">You [User id:{user_id}] do not have access to these records [{record_ids}] from model {model_name}.</field>
            <field name="what_to_do">Please contact support.</field>
            <field name="error_code">805</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_806" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">You [User id:{user_id}] do not have access to model {model_name}.</field>
            <field name="what_to_do">Please contact support.</field>
            <field name="error_code">806</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_807" model="rb_delivery.error_log">
            <field name="error_title">Error while updating order</field>
            <field name="name">You can not set both fields 'Delivery cost on sender' and 'Delivery cost on customer' to True at the same time.</field>
            <field name="what_to_do">Please make sure to keep at least one of the fields set to False.</field>
            <field name="error_code">807</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_1500" model="rb_delivery.error_log">
            <field name="error_title">Error while creating order</field>
            <field name="name">You reached the limit of today orders with this mobile number {mobile_number}.</field>
            <field name="what_to_do">Please try again tommorrow.</field>
            <field name="error_code">1500</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>
        <record id="order_error_1501" model="rb_delivery.error_log">
            <field name="error_title">Error while creating order</field>
            <field name="name">The system reached the limit of today public orders.</field>
            <field name="what_to_do">Please try again tommorrow.</field>
            <field name="error_code">1501</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_1002" model="rb_delivery.error_log">
            <field name="error_title">Cant print report/waybill</field>
            <field name="name">Wait Time has exceeded for printing report/waybill, because the server is loaded</field>
            <field name="what_to_do">Please wait a bit then try again.</field>
            <field name="error_code">1002</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_1003" model="rb_delivery.error_log">
            <field name="error_title">Cant Create/Edit Order</field>
            <field name="name">You are not allowed to create an order with the reference {order_reference} because it is already used for a follow up order that has been created in the system</field>
            <field name="what_to_do">Please use another reference or contact support.</field>
            <field name="error_code">1003</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_1004" model="rb_delivery.error_log">
            <field name="error_title">Cannot Create Communication Log</field>
            <field name="name">Missing required fields: 'order' or 'message Value'.</field>
            <field name="what_to_do">Check the error message, and contact the administration.</field>            
            <field name="error_code">1004</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="order_error_1005" model="rb_delivery.error_log">
            <field name="error_title">Failed to Create Communication Log</field>
            <field name="name">The system was unable to create a communication log entry due to unexpected issues.</field>
            <field name="what_to_do">Check the error message, and contact the administration.</field>        
            <field name="error_code">1005</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="configuration_error_404" model="rb_delivery.error_log">
            <field name="error_title">Error while update client configuration</field>
            <field name="name">Access Denied: Admin Privileges Required</field>
            <field name="what_to_do">Only administrators can modify this configuration. Please contact administrator for assistance.</field>
            <field name="error_code">404</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.client_configuration</field>
        </record>

        <record id="order_error_9001" model="rb_delivery.error_log">
            <field name="error_title">Cannot assign agent to order in collection</field>
            <field name="name">Order {order_sequence} cannot be assigned to another agent because it is already in agent collection {collection_sequence}.</field>
            <field name="what_to_do">Please remove the order from the agent collection first before assigning it to another agent.</field>
            <field name="error_code">9001</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>
        </record>

        <record id="user_error_299" model="rb_delivery.error_log">
            <field name="error_title">Order Status Update Error</field>
            <field name="name">You cannot change the order status because the OTP has not been verified.</field>
            <field name="what_to_do">Please verify the OTP before updating the order status.</field>
            <field name="error_code">299</field>
            <field name="error_type">warning</field>
            <field name="model_name">rb_delivery.order</field>        
        </record>

    </data>
</odoo>