<?xml version="1.0" encoding="utf-8"?>
<odoo>
	<data>
		<record id="cron_archive_old_completed_orders" forcecreate="True" model="ir.cron">
			<field name="name">Archive old completed orders</field>
			<field name="model_id" ref="model_rb_delivery_order"/>
			<field name="state">code</field>
			<field name="active">False</field>
            <field name="code">model.clear_inactive_orders()</field>
            <field name="interval_type">days</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d 3:00:00')" />
            <field name="numbercall">-1</field>
		</record>
		<record id="cron_archive_old_draft_orders" forcecreate="True" model="ir.cron">
			<field name="name">Archive old completed draft orders</field>
			<field name="model_id" ref="model_rb_delivery_order_draft"/>
			<field name="state">code</field>
			<field name="active">False</field>
            <field name="code">model.clear_inactive_draft_orders()</field>
            <field name="interval_type">days</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d 3:00:00')" />
            <field name="numbercall">-1</field>
		</record>
		<record id="cron_archive_old_order_logs" forcecreate="True" model="ir.cron">
			<field name="name">Archive old order logs</field>
			<field name="model_id" ref="model_rb_delivery_order_logs"/>
			<field name="state">code</field>
			<field name="active">False</field>
            <field name="code">model.clear_old_order_logs()</field>
            <field name="interval_type">days</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d 3:00:00')" />
            <field name="numbercall">-1</field>
		</record>
		<record id="cron_archive_old_notification_recs" forcecreate="True" model="ir.cron">
			<field name="name">Archive old notification center</field>
			<field name="model_id" ref="model_rb_delivery_notification_center"/>
			<field name="state">code</field>
			<field name="active">False</field>
            <field name="code">model.archive_old_notification_center()</field>
            <field name="interval_type">months</field>
			<field name="interval_number">6</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d 3:00:00')" />
            <field name="numbercall">-1</field>
		</record>
		<record id="cron_delete_old_tracking_values" forcecreate="True" model="ir.cron">
			<field name="name">Delete old records</field>
			<field name="model_id" ref="model_mail_tracking_value"/>
			<field name="state">code</field>
			<field name="active">False</field>
            <field name="code">model.clear_old_tracking_values()</field>
            <field name="interval_type">days</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d 3:00:00')" />
            <field name="numbercall">-1</field>
		</record>

		<record id="cron_delete_collection_and_retuned_collection_attachment" forcecreate="True" model="ir.cron">
			<field name="name">Delete old collection and returned collection attachment</field>
			<field name="model_id" ref="model_rb_delivery_multi_print_orders_money_collector"/>
			<field name="state">code</field>
			<field name="active">False</field>
            <field name="code">model.clear_old_attachment()</field>
            <field name="interval_type">days</field>
			<field name="interval_number">14</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d 3:00:00')" />
            <field name="numbercall">-1</field>
		</record>
	</data>
</odoo>
