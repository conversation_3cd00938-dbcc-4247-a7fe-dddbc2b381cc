
<odoo>
  <data>
    <!-- inherit module [olivery_vat] -->
    <!-- inherit module [olivery_custody] -->
  <!-- inherit module[olivery_branch_collection] -->
    <record id="view_form_rb_delivery_user" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_user</field>
      <field name="model">rb_delivery.user</field>

      <field name="arch" type="xml">
        <form>

          <header>
          <field name="is_password_manager" invisible='1'/>
          <field name="is_block_delivery_fee" invisible='1'/>
          <field name="is_base" invisible='1'/>
          <field name="is_sales" invisible='1'/>
          <field name="show_online" readonly="1" invisible="1"/>
          <field name="is_confirm_user_manager" invisible='1'/>
          <field name="onboarding_error_ids" invisible="1"/>
              <button string="Change Password" name="change_password" type="object" attrs="{'invisible': ['|','&amp;','&amp;',('is_password_manager','=',False),('is_base','=',False),('is_sales','=',False),'&amp;',('state','!=','confirmed'),('state','!=','reconfirmed')]}"/>
            <!-- Buttons and status widget -->
              <button string="Confirmed" name="action_confirm" type="object" attrs="{'invisible': ['|',('state','!=','pending'),'&amp;','&amp;','&amp;',('is_base','=',False),('is_super_manager','=',False),('is_confirm_user_manager','=',False),('is_sales','=',False)]}"/>
              <!-- <button string="Cancel" name="wkf_action_cancel" type="object" attrs="{'invisible': [('state','=','pending')]}"  groups="rb_delivery.role_super_manager"/> -->
              <button string="Reopen" name="wkf_action_reopen" type="object" attrs="{'invisible': [('state','!=','canceled')]}"  groups="rb_delivery.role_super_manager"/>
              <button string="Deactivate" name="wkf_action_deactivate" type="object" attrs="{'invisible': [('state', '!=', 'confirmed'),('state','!=','reconfirmed')]}" groups="rb_delivery.role_super_manager,rb_delivery.role_sales"/>
              <button string="Reconfirmed" name="wkf_action_reconfirm" type="object" attrs="{'invisible': ['|',('state','!=','deactivate'),'&amp;','&amp;','&amp;',('is_base','=',False),('is_super_manager','=',False),('is_confirm_user_manager','=',False),('is_sales','=',False)]}"/>
              <field name="state" widget="statusbar"  statusbar_visible=" " statusbar_colors='{"pending":"red","closed":"blue"}'  />
              <button string="Migrate" name="wkf_migrate" type="object" invisible="1"/>

          </header>

          <sheet>
            <div class="oe_button_box o_full" name="button_box" style="margin-top:1vh">

              <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive" groups="base.group_system,rb_delivery.role_super_manager,rb_delivery.role_manager">
                <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
              </button>

              <button type="object" name="get_location" class="btn btn-sm oe_stat_button o_form_invisible"  attrs="{'invisible':[('show_location','!=',True)]}">
                <div class="fa fa-fw fa-file o_button_icon"/>
                <div class="o_form_field o_stat_info" data-original-title="" title="">
                  <span class="o_stat_text">Map</span>
                </div>
              </button>
              <button attrs="{'invisible':['|',('create_date','=',False),('role_code','!=','rb_delivery.role_business')]}" type="object" name="get_orders" class="btn btn-sm oe_stat_button o_form_invisible">
                <div class="fa fa-fw fa-list-alt o_button_icon"/>
                  <div class="o_form_field o_stat_info" data-original-title="" title="">
                    <span class="o_stat_text"><field name="number_of_orders"/></span>
                </div>
              </button>
              <button type="object" name="get_notification" class="btn btn-sm oe_stat_button o_form_invisible">
                <div class="fa fa-fw fa-bell o_button_icon"/>
                  <div class="o_form_field o_stat_info" data-original-title="" title="">
                    <span class="o_stat_text">Notifications</span>
                </div>
              </button>
              <button name="toggle_online" type="object" class="oe_stat_button o_form_invisible" attrs="{'invisible':[('show_online','!=',True)]}">
                        <div class="fa fa-power-off o_button_icon"/>
                        <div class="o_form_field o_stat_info" data-original-title="" title="" attrs="{'invisible':[('online','!=',True)]}">
                           <span class="o_stat_text">Online</span>
                        </div>
                        <div class="o_form_field o_stat_info" data-original-title="" title="" attrs="{'invisible':[('online','=',True)]}">
                           <span class="o_stat_text">Ofline</span>
                        </div>
                  </button>
              <button type="object" name="get_logs" class="btn btn-sm oe_stat_button o_form_invisible">
                  <div class="fa fa-fw fa-history o_button_icon"/>
                  <div class="o_form_field o_stat_info" data-original-title="" title="">
                      <span>Order Logs</span>
                  </div>
              </button>
              <button type="object" name="get_onboarding_error_ids" class="btn btn-sm oe_stat_button o_form_invisible"  attrs="{'invisible':[('onboarding_error_ids', '=', [])]}">
                <div class="fa fa-fw fa-warning o_button_icon"/>
                <div class="o_form_field o_stat_info" data-original-title="" title="">
                    <span class="o_stat_text">Issues</span>
                </div>
              </button>
            </div>

           <field name="user_image" widget='image' nolabel="1" class="oe_avatar" />

            <group name="group_top">
              <separator string="User Info :"/>
            </group>


            <group name="group_top">

              <group name="group_left">
                <field name="group_id" required="1" options="{'no_create':True,'no_open':True}" attrs="{'readonly': [('is_role_manager','=',False),('is_base','=',False)]}"/>
                <field name="role_name" string="Role Name"/>
                <field name="username" string="User Name"/>
                <field name="id"/>
                <field name="user_id" options="{'no_open': True}" />
                <field name="create_date" invisible="1"/>
                <field name="is_role_manager" invisible="1"/>
                <field name="is_super_manager" invisible="1"/>
                <field name="is_manager" invisible="1"/>
                <field name="forgot_password"  readonly="1" invisible="1"/>
                <field name="car_number" string="Car Number" attrs="{'invisible': [('role_code','not in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]}"/>
                <field name="role_code" invisible="1"/>
                <field name="commercial_name" attrs="{'invisible': [('role_code','not in',['rb_delivery.role_business','rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]}"/> 
                <field name="commercial_activity" attrs="{'invisible': [('role_code','!=','rb_delivery.role_business')]}"/>
              </group>
            </group>

            <group name="group_top">

              <group name="group_left">

              <field name="is_business" invisible='1'/>
              <field name="is_security_manager" invisible='1'/>
              <field name="is_pricelist_manager" invisible='1'/>
                <field name="pricelist_id" attrs="{'readonly': [('is_pricelist_manager', '=', False),('is_base','=',False),('is_sales','=',False)],'invisible':['|',('role_code','!=','rb_delivery.role_business'),('is_block_delivery_fee','=',True)],'required':[('role_code','=','rb_delivery.role_business')]}"/>
                <field name="previous_pricelist_id" attrs="{'invisible':['|',('role_code','!=','rb_delivery.role_business'),('is_block_delivery_fee','=',True)]}" readonly="1"/>
              </group>

            </group>

            <group name="group_top">
              <group name="group_left">
                <field name="affiliator" attrs="{'invisible':[('role_code','!=','rb_delivery.role_business')],'readonly':[('is_business','=',True)]}" />
                <field name="account_manager_id"/>
              </group>
            </group>

            <group name="group_top">

              <group name="group_left">
                <field name="mobile_number" string="First Mobile Number (Login)" />
                <field name="second_mobile_number" string="Second Mobile Number" />
                <field name="whatsapp_mobile" />
                <field name="second_whatsapp_mobile"/>
                <field name="email_registration" invisible="1"/>
                <field name="email" attrs="{'required':[('email_registration','=',True)]}"/>
                <field name="commercial_number"/>
                <field name="driver_id" attrs="{'invisible': [('role_code', '!=', 'rb_delivery.role_business')]}"/>
              </group>

              <group name="group_right">
                <field name="inclusive_delivery" attrs="{'readonly': [('is_business', '=', True)],'invisible':[('role_code','!=','rb_delivery.role_business')]}"/>
                <field name="address_tag" />
                <field name="show_sub_area" invisible="1"/>
                <field name="sub_area" string="Customer Sub Area"
                  domain="[('show_in_create', '=', True),'|',('parent_id', '=',area_id),('area_parent_id', '=',area_id)]"
                  attrs="{'invisible':[('show_sub_area','!=',True)], 'readonly':[('area_id','=',False)]}"
                  options="{'no_create': True, 'no_create_edit':True}" />
                <field name="area_id" options="{'no_create': True, 'no_create_edit':True}" />
                <field name="show_country" invisible="1"/>
                <field name="country_id" attrs="{'invisible':[('show_country','=',False)]}"
                  options="{'no_create': True, 'no_create_edit':True}" />
                <field name="address"/>
              </group>

            </group>
            <notebook groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales,rb_delivery.role_accounting">

              <page string="Settings" class="settings">
                <group class="settings_group" string="User settings" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales" attrs="{'invisible': [('role_code', '!=', 'rb_delivery.role_business')]}">
                  <field name="has_customers"/>
                  <field name="user_parent_id" domain="[('role_code','=',role_code)]" string="Main Address/Main User" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales"/>
                  <field name="collection_in_main_user_name" attrs="{'invisible':[('child_ids','=',False)]}"/>
                  <field name="hide_totals_from_dashboard" attrs="{'invisible':[('child_ids','in',[False, []])]}"/>
                  <field name="child_ids" string="Addresses/Sub Users" attrs="{'readonly': [('is_super_manager', '=', False),('is_manager', '=', False),('is_base', '=', False)]}" domain="[('role_code','=',role_code)]" widget="many2many">
                    <tree delete="1">
                      <field name="username"/>
                      <field name="mobile_number"/>
                      <field name="pricelist_id"/>
                      <field name="area_id"/>
                      <field name="sub_area" options="{'no_create': True, 'no_edit': True, 'no_delete': True}" />
                      <field name="address"/>
                    </tree>
                  </field>
                </group>
              </page>
              <page string="Location" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales" attrs="{'invisible':[('show_location','!=',True)]}">
                <group>
                  <field name="show_location" invisible="1"/>
                  <field name="location_link" widget="url"/>
                  <field name="latitude"/>
                  <field name="longitude"/>
                </group>
              </page>

              <page string="Accounting" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales,rb_delivery.role_accounting">
                <group name="group_left">
                  <field name="is_per_business" attrs="{'invisible':[('user_is_driver','=','False')]}"/>
                   <field name="pricing_type" />
                   <field name="pricing_fixed_value" string="Fixed value" attrs="{'invisible':[('pricing_type','!=','fixed')]}"/>
                   <field name="pricing_percentage_value" string="Percentage value" attrs="{'invisible':[('pricing_type','!=','percentage')]}"/>
                   <field name="pricing_pricelist_id" string="Pricelist" attrs="{'invisible':[('pricing_type','!=','pricelist')]}"/>
                </group>
                <group name="group_left" string="Picked Up Pricing" attrs="{'invisible':[('role_code','not in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]}">
                  <field name="picked_up_by_pricing_type" />
                  <field name="picked_up_by_pricing_fixed_value" attrs="{'invisible':[('picked_up_by_pricing_type','!=','fixed')]}"/>
                  <field name="picked_up_by_pricing_percentage_value" attrs="{'invisible':[('picked_up_by_pricing_type','!=','percentage')]}"/>
                  <field name="picked_up_by_pricing_pricelist_id" attrs="{'invisible':[('picked_up_by_pricing_type','!=','pricelist')]}"/>
               </group>
                <group name="group_right">
                <field name="default_payment_type"/>
                <field name="driver_commission_value_picked_up" attrs="{'invisible':[('role_code','=','rb_delivery.role_driver')]}"/>
                <field name="driver_commission_value_delivered" attrs="{'invisible':[('role_code','=','rb_delivery.role_driver')]}"/>
                <field name="default_payment_detail" attrs="{'invisible':[('default_payment_type','=',False)]}"/>
                <field name="bank_name"/>
                <field name="holder_name"/>
                <field name="bank_number"/>
                <field name="wallet_name"/>
                <field name="wallet_number"/>
              </group>
              <group class="commission" name="group_top" attrs="{'invisible': [('role_code', '=', 'rb_delivery.role_business')]}">
                <separator string="Delivery Commission"/>
                <group name="group_left">
                  <field name="user_request_commission" />
                  <field name="commission_type" attrs="{'invisible':[('user_request_commission','=',False)]}"/>
                  <field name="commission_value" attrs="{'invisible':[('user_request_commission','=',False)]}"/>
                </group>
              </group>
              </page>

              <page string="Insurance" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales" attrs="{'invisible': [('role_code','not in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative'])]}">
                <group>
              <field name="insurance_type"/>
              <field name="insurance_value" attrs="{'invisible': [('insurance_type', '=', 'promissory_note')]}"/>
              <field name="insurance_attachment" attrs="{'invisible': [('insurance_type', '=', 'cash')]}"/>
              </group>
              </page>

              <page string="Feature" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales">
                <group>
                <field name="show_address_in_waybill" attrs="{'invisible': [('role_code','!=','rb_delivery.role_business')]}"/>
                <field name="show_area_in_waybill" attrs="{'invisible': [('role_code','!=','rb_delivery.role_business')]}"/>
                </group>
                <group>
                  <field name="print_user_logo_in_bolisa" attrs="{'invisible': [('role_code','!=','rb_delivery.role_business')]}"/>
                  <field name="replace_company_logo_with_business_logo" attrs="{'invisible': [('role_code','!=','rb_delivery.role_business')]}"/>
                </group>
                <group>
                  <field name="show_request_collection_button" attrs="{'invisible': [('role_code','!=','rb_delivery.role_business')]}"/>
                </group>
              </page>

              <page string="Area Map" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales" attrs="{'invisible': [('role_code','!=','rb_delivery.role_business')]}">
                <button name="sync_areas" type="object" string="Sync Areas" class="oe_highlight" attrs="{'invisible': [('is_company', '=', False)]}" />
                <group>
                <field name="area_map" widget="one2many_list"/>
                <field name="sub_area_map" widget="one2many_list"/>
                </group>
              </page>

               <page name="security" class="security" string="Security" groups="base.group_system,rb_delivery.role_security_manager">
                <group name="group_top">
                  <!-- inherit module [olivery_barcode_generator] -->
                  <group name="group_right">
                    <field name="user_is_business" invisible="1"/>
                    <field name="user_is_driver" invisible="1"/>
                    <field name="password_manager" attrs="{'invisible':['|',('user_is_business','=',True),('user_is_driver','=',True)]}"/>
                    <field name="pricelist_manager" attrs="{'invisible':['|',('user_is_business','=',True),('user_is_driver','=',True)]}"/>
                    <field name="role_manager" attrs="{'invisible':['|',('user_is_business','=',True),('user_is_driver','=',True)]}"/>
                    <field name="confirm_user_manager" attrs="{'invisible':['|',('user_is_business','=',True),('user_is_driver','=',True)]}"/>
                    <field name="pricelist_edit_manager" attrs="{'invisible':['|','|',('user_is_business','=',True),('user_is_driver','=',True),('user_id','=',False)]}"/>
                    <field name="collection_manager" attrs="{'invisible':['|','|',('user_is_business','=',True),('user_is_driver','=',True),('user_id','=',False)]}"/>
                    <field name="returned_collection_manager" attrs="{'invisible':['|','|',('user_is_business','=',True),('user_is_driver','=',True),('user_id','=',False)]}"/>
                    <field name="block_delivery_fee"/>
                    <field name="block_delivery_profit"/>
                    <field name="allow_edit_collection_orders" attrs="{'invisible':['|','|',('user_is_business','=',True),('user_is_driver','=',True),('user_id','=',False)]}"/>
                    <field name="settings_manager" attrs="{'invisible':[('role_code','=','rb_delivery.role_super_manager')]}"/>
                  </group>
                  <group name="group_left">
                    <field name="show_fields_button"  attrs="{'invisible':[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative','rb_delivery.role_super_manager','rb_delivery.role_manager','rb_delivery.role_picking_up_manager','rb_delivery.role_distribution_manager'])]}"/>
                    <field name ="show_editable_button" attrs="{'invisible':[('role_code','in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative','rb_delivery.role_super_manager','rb_delivery.role_manager','rb_delivery.role_picking_up_manager','rb_delivery.role_distribution_manager'])]}"/>
                  </group>

              </group>

              </page>
              <page string="More" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales">
                <group>
                <field name="order_type" attrs="{'invisible':[('user_is_business','=',False)]}"/>
                <!-- <field name="business_work_category" attrs="{'invisible':[('user_is_business','=',False)]}"/> -->
                <field name="field_ids" widget="many2many_tags"/>
                  <field name="player_id" string="Player ID" attrs="{'readonly':[('is_base','=',False)]}"/>
                  <field name="online" readonly="1" attrs="{'invisible': [('show_online','!=','True')]}"/>

                <field name="returned_discount_type"/>
                 <field name="fixed_discount" attrs="{'invisible': [('returned_discount_type','!=','1')]}"/>

                <field name="canceled_discount_type"/>
                <field name="fixed_canceled_discount" attrs="{'invisible': [('canceled_discount_type','!=','1')]}"/>
                </group>
              </page>
              <page string="Social" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales">
                <group>
                <field name="website" widget="url"/>
                <field name="facebook_hyperlink" widget="url"/>
                <field name="linkedin_hyperlink" widget="url"/>
                <field name="instagram_hyperlink" widget="url"/>
                <field name="twitter_hyperlink" widget="url"/>
                <field name="tiktok_hyperlink" widget="url"/>
                </group>
                </page>
                
              <page class="vhub" string="V-Hub" groups="rb_delivery.role_super_manager,rb_delivery.role_manager,rb_delivery.role_picking_up_manager,rb_delivery.role_distribution_manager,rb_delivery.role_sales" attrs="{'invisible': [('role_code','not in',['rb_delivery.role_driver','rb_delivery.role_sort_and_distribute_representative','rb_delivery.role_picking_up_representative','rb_delivery.role_business','rb_delivery.role_delivery_company'])]}">
              <group>
                <field name="is_company" attrs="{'readonly': [('is_business', '=', True)]}"/>
                <field name="show_follower_vhub" attrs="{'invisible':[('role_code','!=','rb_delivery.role_business')]}"/>
                <field name="company_username" attrs="{'invisible': [('is_company', '=', False)]}"/>
                <field name="company_password" password = 'True' attrs="{'invisible': [('is_company', '=', False)]}"/>
                <field name="company_url" placeholder="For example: https://your-company-name.olivery.app" attrs="{'readonly': [('is_business', '=', True)]}"/>
                <field name="company_db" attrs="{'readonly': [('is_business', '=', True)]}"/>
              </group>
              </page>

              <page class="defaults" string="External Defaults" attrs="{'invisible': [('role_code','!=','rb_delivery.role_business')]}">
                <group>
                  <field name="default_area_id" options="{'no_create': True, 'no_create_edit':True}" />
                  <field name="default_sub_area_id" options="{'no_create': True, 'no_create_edit':True}" />
                </group>
              </page>
            </notebook>
            <notebook groups="rb_delivery.role_business">
            <page string="Accounting" >
              <group name="group_right" groups="rb_delivery.role_business">
              <field name="default_payment_type"/>
              <field name="default_payment_detail" attrs="{'invisible':[('default_payment_type','=',False)]}"/>
              <field name="bank_name"/>
              <field name="holder_name"/>
              <field name="bank_number"/>
              <field name="wallet_name"/>
              <field name="wallet_number"/>
            </group>
            </page>
            <page string="Social">
              <group>
                <field name="website" widget="url" />
                <field name="facebook_hyperlink" widget="url" />
                <field name="linkedin_hyperlink" widget="url" />
                <field name="instagram_hyperlink" widget="url" />
                <field name="twitter_hyperlink" widget="url" />
                <field name="tiktok_hyperlink" widget="url" />
              </group>
            </page>
          </notebook>
          </sheet>
          <!-- History and communication: -->
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>

      </field>
    </record>


    <record id="view_tree_rb_delivery_user" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_user</field>
      <field name="model">rb_delivery.user</field>
        <!-- <field name="user_sequence"/> -->

      <field name="arch" type="xml">
        <tree decoration-danger="state=='pending'">
          <field name="state" invisible="1" />
          <field name="username" />
         <field name="commercial_name"/>
          <field name="role_name" string="Role Name"/>
          <field name="mobile_number"/>
          <field name="pricelist_id" attrs="{'column_invisible':1}"/>
          <field name="area_id" options="{'no_create': True, 'no_edit': True, 'no_delete': True}" />
          <field name="sub_area" options="{'no_create': True, 'no_edit': True, 'no_delete': True}" />
          <field name="address"/>
          <field name="country_id" options="{'no_create': True, 'no_edit': True, 'no_delete': True}"
            invisible="1" />
         </tree>

      </field>

    </record>
  <record id="view_tree_rb_delivery_user_pricelist_manager" model="ir.ui.view">
            <field name="name">view_tree_rb_delivery_user_pricelist_manager</field>
            <field name="model">rb_delivery.user</field>
            <field name="inherit_id" ref="view_tree_rb_delivery_user"/>
            <field name="groups_id" eval="[(6, 0, [ref('rb_delivery.role_pricelist_manager') ])]"/>
            <field name="arch" type="xml">
                <field name="pricelist_id" position="attributes">
                    <attribute name="attrs">{'column_invisible':0}</attribute>
                </field>
            </field>
        </record>
    <record id="view_search_rb_delivery_user" model="ir.ui.view">
      <field name="name">view_tree_rb_delivery_user</field>
      <field name="model">rb_delivery.user</field>

      <field name="arch" type="xml">
        <search>
          <group>
            <field name="username" filter_domain="['|',('username','ilike',self),('commercial_name','ilike',self)]"/>
            <field name="role_name" string="Role Name"/>
            <field name="mobile_number"/>
            <field name="second_mobile_number"/>
            <field name="email"/>
            <field name="address"/>
            <field name="state"/>
            <field name="pricelist_id"/>
            <field name="previous_pricelist_id"/>
            <field name="business_work_category"/>
          </group>
          <group string="categorize by active status">
            <filter name="user_active" string="Active Users" domain="[('active','=',True)]"/>
            <filter name="user_deactivate" string="Deactivate Users" domain="['|',('state','=','deactivate'),('active','=',False)]"/>

          </group>
          <group string="Groups">
            <filter name="group_by_group_id" string="By Role" domain="[ ]" context="{'group_by': 'group_id'}" />
            <filter name="group_by_state" string="By State" icon="terp-partner" context="{'group_by':'state'}"/>
            <filter name="group_by_create_uid" string="By Creator" icon="terp-partner" context="{'group_by':'create_uid'}"/>
            <filter name="group_by_create_date" string="By Create Date" icon="terp-partner" context="{'group_by':'create_date'}"/>
            <filter name="group_by_area" string="By Area" domain="[ ]" context="{'group_by': 'area_id'}" />
            <filter name="group_by_sub_area" string="By Sub Area" domain="[ ]" context="{'group_by': 'sub_area'}" />
            <filter name="group_by_inclusive" string="By Inclusive delivery" context="{'group_by': 'inclusive_delivery'}" />
            <filter name="group_by_pricelist_id" string="By Pricelist" context="{'group_by': 'pricelist_id'}" />
            <filter name="group_by_previous_pricelist_id" string="By Previous Pricelist" context="{'group_by': 'previous_pricelist_id'}" />
            <filter name="group_by_number_of_orders" string="By Number of Orders" context="{'group_by': 'number_of_orders'}" />
            <filter name="group_by_business_work_category" string="By Business Work Category" domain="[ ]" context="{'group_by': 'business_work_category'}" />
            <filter name="group_by_country" string="By Country" context="{'group_by': 'country_id'}" />

          </group>
        </search>
      </field>

    </record>
    <record id="view_form_rb_delivery_multi_select_confirm" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_multi_select_confirm</field>
      <field name="model">rb_delivery.select_confirm</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
            <!-- <button name="select_confirm" confirm="Do you want to proceed?" type="object" string="Confirm users" class="oe_highlight" /> -->
          </header>
          <sheet>
            <group name="group_top">
              <separator string="Are you want to confirm ? "/>
            </group>
          </sheet>

          <footer>
          <button name="select_confirm" type="object" string="Save"/>
          <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>

        <record id="view_form_rb_delivery_multi_select_reconfirm" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_multi_select_reconfirm</field>
      <field name="model">rb_delivery.select_reconfirm</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
            <!-- <button name="select_confirm" confirm="Do you want to proceed?" type="object" string="Confirm users" class="oe_highlight" /> -->
          </header>
          <sheet>
            <group name="group_top">
              <separator string="Are you want to reconfirm ? "/>
            </group>
          </sheet>

          <footer>
          <button name="select_reconfirm" type="object" string="Save"/>
          <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>

    <record id="view_form_rb_delivery_multi_select_archive" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_multi_select_archive</field>
      <field name="model">rb_delivery.select_archive</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
            <!-- <button name="select_confirm" confirm="Do you want to proceed?" type="object" string="Confirm users" class="oe_highlight" /> -->
          </header>
          <sheet>
            <group name="group_top">
              <separator string="Are you sure you want to archive ? "/>
            </group>
          </sheet>

          <footer>
          <button name="select_archive" type="object" string="Save"/>
          <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>

    <record id="view_form_rb_delivery_multi_select_unarchive" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_multi_select_unarchive</field>
      <field name="model">rb_delivery.select_unarchive</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
            <!-- <button name="select_confirm" confirm="Do you want to proceed?" type="object" string="Confirm users" class="oe_highlight" /> -->
          </header>
          <sheet>
            <group name="group_top">
              <separator string="Are you sure you want to unarchive ? "/>
            </group>
          </sheet>

          <footer>
          <button name="select_unarchive" type="object" string="Save"/>
          <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>


    <record id="view_form_rb_delivery_multi_select_pricelist" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_multi_select_pricelist</field>
      <field name="model">rb_delivery.select_pricelist</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
          </header>

          <sheet>

            <group name="group_top">
              <field name="pricelist" />
            </group>

          </sheet>
          <footer>
            <button name="select_pricelist" type="object" string="Select Pricelist" class="oe_highlight" />
          </footer>

        </form>

      </field>
    </record>

    <record id="view_form_rb_delivery_multi_select_previous_pricelist" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_multi_select_previous_pricelist</field>
      <field name="model">rb_delivery.select_previous_pricelist</field>

      <field name="arch" type="xml">

        <form create="false" edit="false">

          <header>
          </header>

          <sheet>

            <group name="group_top">
            <separator string="Are you sure?"/>
            </group>

          </sheet>
          <footer>
            <button name="select_previous_pricelist" type="object" string="Select Previous Pricelist" class="oe_highlight" />
            <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>

        </form>

      </field>
    </record>

    <record id="view_form_rb_delivery_notify_users" model="ir.ui.view">

      <field name="name">view_form_rb_delivery_notify_users</field>
      <field name="model">rb_delivery.notify_users</field>

      <field name="arch" type="xml">
        <form create="false" edit="false">
          <header>
            <!-- Buttons and status widget -->
          </header>

          <sheet>
            <group name="group_top">
                <field name="header" placeholder="Header" attrs="{'required':[('notification_type','!=','is_announcement')]}"/>
                <field name="message" placeholder="Message" attrs="{ 'required':['|',('template', '=', False), ('notification_type','!=','is_announcement')]}"/>
                <field name="images" attrs="{'invisible':[('notification_type','!=','is_announcement')]}" string="Images (png,jpg,jpeg,gif)" widget="image" options="{'width': 100, 'height': 100}"/>
                <field name="link" attrs="{'invisible':[('notification_type','!=','is_announcement')]}"/>
                <field name="template" attrs="{'invisible':[('notification_type','!=','is_email')]}"/>
                <field name="notification_type" widget="radio"/>
            </group>
          </sheet>
           <footer>
          <button name="notify" type="object" string="Notify"/>
          <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
          </footer>
        </form>

      </field>
    </record>

    <record id="view_form_rb_delivery_add_password" model="ir.ui.view">

            <field name="name">view_form_rb_delivery_add_password</field>
            <field name="model">rb_delivery.add_password</field>

            <field name="arch" type="xml">

                <form create="false" edit="false">

                    <sheet>

                        <group name="group_top">
                            <field name="password"/>
                        </group>

                    </sheet>
                    <footer>
                        <button name="save" type="object" string="Save"/>
                        <button name="cancel" string="Cancel" special="cancel" class="oe_link"/>
                    </footer>

                </form>

      </field>
    </record>

  </data>
</odoo>
