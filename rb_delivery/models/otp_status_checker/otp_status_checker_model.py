# -*- coding: utf-8 -*-

import re
from openerp import models, fields, api, _
import random
import string
import logging
_logger = logging.getLogger(__name__)

class otp_status_checker(models.Model):

    _name = 'rb_delivery.otp_status_checker'
    _description = "OTP Status Checker"
    _inherit = 'mail.thread'


    def get_groups(self):
        group_ids = self.env['rb_delivery.client_configuration'].get_param('hidden_roles')
        domain = [('category_id.code','=','model_rb_delivery')]
        if group_ids:
            domain.append(('id','not in', group_ids))
        return domain

    # ----------------------------------------------------------
    # Database
    # ----------------------------------------------------------

    otp_statuses = fields.Many2many('rb_delivery.status',string="OTP Status",track_visibility="on_change", domain=[('status_type', '=', 'olivery_order')])

    groups = fields.Many2many('res.groups', string="Groups", track_visibility="on_change", domain=get_groups)

    send_through = fields.Selection(selection='get_otp_provider', string="Send Through", track_visibility="on_change")

    message = fields.Text(string="Message", track_visibility="on_change", help="Message to be sent with OTP")

    email_title = fields.Char(string="Email Title", default="OTP Verification", track_visibility="on_change", help="Title of the email to be sent")

    otp_length = fields.Integer(string="OTP Length", default=6, help="Length of the OTP code")

    bypass_otp = fields.Boolean(string="Bypass OTP (If failed)", default=False)


    def get_otp_provider(self):
        return [('sms', 'SMS')]
    
    def generate_otp(self):
        otp_length = self.otp_length or 6
        return ''.join(random.choices(string.digits, k=otp_length))
    
    def send_otp(self, recipient, record=None):
        otp_code = self.generate_otp()
        result = self.send_otp_notification(recipient, otp_code=otp_code, record=record)
        if isinstance(result, dict):
            result['bypass_otp'] = False # TODO: This should be set based on actual logic
            result['otp_code'] = otp_code
        return otp_code, result

    def send_otp_notification(self, recipient, recipient_email=None, otp_code=None, record=None):
        if not otp_code:
            otp_code = self.generate_otp()
            
        if self.send_through == 'sms':
            return self._send_otp_via_sms(recipient, otp_code, record)
        elif self.send_through == 'email':
            if not recipient_email:
                return {'status': 'error', 'message': 'EMAIL_IS_REQUIRED_FOR_EMAIL_OTP'}
            return self._send_otp_via_email(recipient_email, otp_code, record)
        else:
            return {'status': 'error', 'message': _('Invalid OTP sending method: %s') % self.send_through}
    
    def _send_otp_via_sms(self, mobile_number, otp_code, record=None):
        try:
            new_msg = self.message.replace('{otp_code}', otp_code)
            variables = re.findall(r"(\{[a-z_A-Z0-9_.]+\})", new_msg)
            message = self.env['rb_delivery.notification_center'].get_message(new_msg, variables, False, record, 'rb_delivery.order')
            sms_model = self.env['rb_delivery.sms']
            
            sms_model.sudo().send_sms(message, mobile_number, raise_exception=True)
            return {'status': 'success', 'message':'OTP_SENT_SUCCESSFULLY_VIA_SMS'}
        except Exception as e:
            _logger.error("Failed to send OTP via SMS: %s", str(e))
            return {'status': 'error', 'message': _('Failed to send OTP via SMS: %s') % str(e)}
    
    def _send_otp_via_email(self, email, otp_code, record=None):
        try:
            subject = _('Your OTP Verification Code')
            new_msg = self.message.replace('{otp_code}', otp_code)
            variables = re.findall(r"(\{[a-z_A-Z0-9_.]+\})", new_msg)
            message = self.env['rb_delivery.notification_center'].get_message(new_msg, variables, False, record, 'rb_delivery.order')
            
            mail_values = {
                'subject': subject,
                'body_html': message,
                'email_to': email,
            }
            mail = self.env['mail.mail'].create(mail_values)
            mail.send()
            
            return {'status': 'success', 'message': 'OTP_SENT_SUCCESSFULLY_VIA_EMAIL'}
        except Exception as e:
            _logger.error("Failed to send OTP via email: %s", str(e))
            return {'status': 'error', 'message': _('Failed to send OTP via email: %s') % str(e)}

