# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
import json

class rb_delivery_mobile_card_item(models.Model):

    _name = 'rb_delivery.mobile_card_item'
    _order = "sequence ASC"

    # ----------------------------------------------------------------------
    # Database
    # ----------------------------------------------------------------------

    LOCAL_COMPUTE_FIELDS = {
        'senderLocation': ['business_area', 'business_sub_area'],
        'customerLocation': ['customer_area', 'customer_sub_area'],
        'referenceSequence': ['reference_id', 'sequence'],
        'collectionExtraFields': ['status_color', 'secondary_status_color', 'business_longitude', 'business_latitude', 'mobile_number', 'second_business_mobile_number', 'business_whatsapp_mobile', 'business_second_whatsapp_mobile', 'business_mobile_number', 'agent_whatsapp_mobile', 'agent_second_whatsapp_mobile', 'agent_mobile_number', 'second_agent_mobile_number', 'cus_whatsapp_mobile', 'cus_second_whatsapp_mobile', 'longitude', 'latitude']
    }

    
    card_creator = fields.Many2one('rb_delivery.mobile_card_creator')

    card_creator_fields = fields.Many2many('ir.model.fields',related="card_creator.fields_ids")

    unique_field_name = fields.Char(compute = "_compute_unique_field_name", store=True)

    is_card_container = fields.Boolean()

    card_items = fields.One2many('rb_delivery.mobile_card_item',inverse_name="card_item")

    card_item = fields.Many2one('rb_delivery.mobile_card_item')

    item_actions = fields.One2many('rb_delivery.mobile_item_actions',inverse_name="card_item")

    has_card_items = fields.Boolean(compute="_compute_has_card_items", store=True)

    card_item_model = fields.Many2one('ir.model',related='card_item.model')

    model = fields.Many2one('ir.model' , related='card_creator.model')

    field = fields.Many2one('ir.model.fields')
    
    field_relation = fields.Char(related="field.relation")

    field_ttype = fields.Selection(related="field.ttype")

    label = fields.Char()
    
    title = fields.Char()

    is_label = fields.Boolean()

    is_button = fields.Boolean()

    is_empty_feild = fields.Boolean()
    
    hide_empty_field = fields.Boolean(string="Hide Field If The Content Is Empty")
    
    replace_empty_field = fields.Char(string="Default Value If The Field Is Empty")

    button_icon_selction = fields.Many2one('rb_delivery.icons')

    button_icon = fields.Char(default="edit")

    button_function = fields.Many2one("rb_delivery.card_model_functions")

    button_theme = fields.Selection([('small','Small'),('big','Big')],default='small')

    is_hyperlink = fields.Boolean()

    hyperlink_type = fields.Selection([('mailto','Email'),('tel','Mobile'),(False,'URL'),('javascript','Javascript')])

    hyperlink_field = fields.Many2one('ir.model.fields')

    have_image = fields.Boolean(compute="_get_field_have_image",store=True)

    image_field_name = fields.Many2one("ir.model.fields")

    have_color = fields.Boolean(compute="_get_field_have_color",store=True)

    color_field_name = fields.Many2one("ir.model.fields")

    secondary_color_field_name = fields.Many2one("ir.model.fields")

    color = fields.Char()

    background = fields.Char()

    sequence = fields.Integer()

    position = fields.Selection([('header','Header'),('content','Content'),('footer','Footer')],default="content")

    sub_position = fields.Many2one('rb_delivery.card_sub_position')

    sub_position_name = fields.Char(string='Sub position name' , related='sub_position.name')

    is_monetary = fields.Boolean()

    invisible_domain = fields.Char()

    user_invisible_domain = fields.Char('User invisible domain')

    font_color = fields.Char('Font Color')

    local_compute_function = fields.Selection([('senderLocation','Sender Location'),('customerLocation','customerLocation'), ('collectionExtraFields', 'Collection Extra Fields Color'), ('referenceSequence', 'Reference Sequence Content')])

    copy_when_clicked = fields.Boolean(string='Copy when clicked')

    keep_show_while_collapse  = fields.Boolean()
    
    @api.onchange('is_button','is_label','is_empty_feild')
    def reset_field_and_card_items(self):
        if self.is_button or self.is_label or self.is_empty_feild:
            self.card_items=[[6,0,[]]]
            self.field=False

    @api.onchange('card_items','field')
    def reset_field_or_card_items(self):
        if self.field and self.field.id:
            self.card_items=[[6,0,[]]]
        elif len(self.card_items)>0:
            self.field=False

    @api.onchange('button_icon_selction')
    def button_icon_selection(self):
        if self.button_icon_selction:
            self.button_icon = self.button_icon_selction.name

    @api.onchange('position')
    def sub_position_selection(self):
        sub_positions=[]
        if self.position=='header' :
            sub_positions.append('header')
        elif self.position=='footer':
            sub_positions=['footer']
        elif self.position=='content':
            sub_positions.append('content')
        return {'domain': {'sub_position': [('parent_position', 'in', sub_positions)]}}

    @api.one
    @api.depends('image_field_name')
    def _get_field_have_image(self):
        if self.image_field_name:
            self.have_image=True

    @api.one
    @api.depends('color_field_name')
    def _get_field_have_color(self):
        if self.color_field_name:
            self.have_color=True


    @api.onchange('card_items')
    def validate_card_items_length(self):
        if len(self.card_items)>3:
            self.env['rb_delivery.error_log'].raise_olivery_error(580,self.id,{})

    @api.onchange('item_actions')
    def validate_item_actions_length(self):
        if len(self.item_actions)>3:
            self.env['rb_delivery.error_log'].raise_olivery_error(580,self.id,{})

    @api.multi
    def name_get(self):
        display_name_result = []
        for rec in self:
            display_name = rec.field.name or ""
            if not display_name:
                if rec.is_button:
                    display_name = _('Button')
                elif rec.is_label:
                    display_name = rec.label
                elif rec.is_empty_feild:
                    display_name = ' '
                elif len(rec.card_items)>0:
                    for card_item in rec.card_items:
                        display_name+=(" "+(card_item.display_name or ""))
            display_name_result.append((rec.id, display_name))

        return display_name_result

    
    @api.multi
    @api.depends('card_creator','field','is_button','button_function','is_label','label','card_items')
    def _compute_unique_field_name(self):
        for rec in self:
            if rec.card_creator and rec.card_creator.name:
                if rec.field:
                    rec.unique_field_name = rec.card_creator.name + '_' + rec.field.name
                elif rec.is_button and rec.button_function and rec.button_function.technical_name:
                    rec.unique_field_name = rec.card_creator.name + '_' + rec.button_function.technical_name
                elif rec.is_label and rec.label:
                    rec.unique_field_name = rec.card_creator.name + '_' + rec.label
                else: 
                    unique_field_name = rec.card_creator.name
                    for card_item in rec.card_items:
                        if card_item.field:
                            unique_field_name += '_' + card_item.field.name
                        elif card_item.is_button and card_item.button_function and card_item.button_function.technical_name:
                            unique_field_name += '_' +  card_item.button_function.technical_name
                        elif card_item.is_label and card_item.label:
                            unique_field_name += '_' + card_item.label
                    rec.unique_field_name = unique_field_name
    @api.multi
    @api.depends('card_items')
    def _compute_has_card_items(self):
        for record in self:
            if record.card_items:
                record.has_card_items = True
            else:
                record.has_card_items = False 

    @api.model
    def get_card(self,card_name):
        group_id = self.env['rb_delivery.user'].sudo().search([['user_id','=',self._uid]]).group_id.id
        card = self.env['rb_delivery.mobile_card_creator'].sudo().search([['card_name','=',card_name],['group_id','=',group_id]])
        if not card:
            card = self.env['rb_delivery.mobile_card_creator'].sudo().search([['card_name','=',card_name],['group_id','=',False]])
        card_items = card.card_items
        header={'show_collapse':card.show_collapse,'default_collapsed':card.default_collapsed}
        header_bubles = []
        header_sub_bubles = []
        content=[]
        side_content=[]
        notes_content=[]
        footer=[]
        selected_compute_functions = []
        many2many_relations={}
        for card_item in card_items:
            if card_item.user_invisible_domain and len(json.loads(card_item.user_invisible_domain.lower())):
                invisible = self.env['rb_delivery.user'].search([['user_id','=',self._uid]]+json.loads(card_item.user_invisible_domain.lower()))
            else:
                invisible = False
            if invisible:
                continue
            if card_item.position=='header':
                if len(card_item.card_items)>0:
                    field_name=''
                    for splitted_item in card_item.card_items:
                        field_name+=self.get_field_name(splitted_item)
                else:
                    field_name=self.get_field_name(card_item)
                
                if card_item.sub_position.name in ['bubble', 'sub_bubble']:
                    bubble = {
                        'bubble_text':field_name,
                        'bubble_color':card_item.background,
                        'function_name':card_item.button_function.technical_name,
                        'is_monetary':card_item.is_monetary,
                        'icon':card_item.button_icon,
                        'copy_when_clicked':card_item.copy_when_clicked,
                        'field_type':card_item.field_ttype,
                        'font_color': card_item.font_color
                    }
                    if card_item.color_field_name:
                        bubble['color_field_name']=card_item.color_field_name.name
                    if card_item.secondary_color_field_name: 
                        bubble['secondary_color_field_name'] = card_item.secondary_color_field_name.name
                    if card_item.local_compute_function:
                        bubble['local_compute_function'] = card_item.local_compute_function
                        if card_item.local_compute_function not in selected_compute_functions:
                            selected_compute_functions.append(card_item.local_compute_function)
                    if card_item.sub_position.name == 'bubble':
                        header_bubles.append(bubble)
                    else:
                        header_sub_bubles.append(bubble)
                    if card_item.field_ttype == 'selection':
                        selection_items = self.env[card_item.model.model].fields_get([field_name])
                        if selection_items and selection_items.get(field_name):
                            selection_items_values = selection_items.get(field_name)
                            if 'selection' in selection_items_values:
                                bubble['selection_items'] =  selection_items_values['selection']
                elif card_item.sub_position.name=='title':
                    if not 'title' in header:
                        header['title']=[]
                    header['title'].append({'value':field_name,'icon':card_item.button_icon,'color':card_item.color,'is_monetary':card_item.is_monetary})
                elif card_item.sub_position.name=='sub_title':
                    if not 'sub_title' in header:
                        header['sub_title']=[]
                    header['sub_title'].append({'value':field_name,'color':card_item.color,'is_monetary':card_item.is_monetary})
            elif card_item.position=='content':
                row=[]
                if len(card_item.card_items)>0:
                    for splitted_item in card_item.card_items:
                        if splitted_item.is_button:
                            row.append({'button':{'icon':splitted_item.button_icon,'text':splitted_item.label,'function_name':splitted_item.button_function.technical_name,'is_empty_feild': card_item.is_empty_feild,'title':card_item.title}})
                        elif splitted_item.is_label:
                            row.append({'name':splitted_item.label,'color': splitted_item.background,'is_monetary':splitted_item.is_monetary,'is_empty_feild': card_item.is_empty_feild,'title':card_item.title})
                        elif splitted_item.is_empty_feild:
                            row.append({'is_empty_feild':splitted_item.is_empty_feild})

                        elif splitted_item.is_hyperlink:
                            row.append({'value': self.get_field_name(card_item),'hyperlink': card_item.hyperlink_field.name,'color': card_item.background,'icon': card_item.button_icon,'is_hyperlink':True,'hyperlink_type':card_item.hyperlink_type,'color': splitted_item.background,'is_empty_feild': card_item.is_empty_feild})
                        else:
                            row.append({'icon':splitted_item.button_icon,'value':self.get_field_name(splitted_item),'color': splitted_item.background,'is_monetary':splitted_item.is_monetary,'is_empty_feild': card_item.is_empty_feild})
                            if splitted_item.invisible_domain:
                                row[0].update({'invisible_domain':splitted_item.invisible_domain})
                else:
                    if card_item.is_button:
                        row=[
                            {'name': card_item.label},
                            { 'button': { 'icon': card_item.button_icon, 'text': self.get_field_name(card_item),'function_name':card_item.button_function.technical_name,'label':card_item.label,'is_empty_feild': card_item.is_empty_feild,'title':card_item.title} }
                        ]
                    elif card_item.is_hyperlink:
                        row =[
                            {'name': card_item.label},
                            {'name': self.get_field_name(card_item),'hyperlink': card_item.hyperlink_field.name,'color': card_item.background,'icon': card_item.button_icon,'is_hyperlink':True,'hyperlink_type':card_item.hyperlink_type,'is_empty_feild': card_item.is_empty_feild,'title':card_item.title}
                        ]
                    else:
                        action_items=[]
                        for action_item in card_item.item_actions:
                            action_items.append({ 'button': { 'icon': action_item.button_icon,'icon_selection':action_item.button_icon_selction, 'function_name':action_item.button_function.technical_name,'hyperlink_type': action_item.action_type,'field_1':action_item.action_field_1.name,'field_2':action_item.action_field_2.name,'title':card_item.title} })
                        row=[
                            {'name': card_item.label,'color': card_item.background},
                            {'name': self.get_field_name(card_item),'field_type':card_item.field_ttype,'color': card_item.background,'icon': card_item.button_icon,'hide_empty_field':card_item.hide_empty_field,'replace_empty_field':card_item.replace_empty_field,'is_monetary':card_item.is_monetary,'is_empty_feild': card_item.is_empty_feild,'title':card_item.title,'keep_show_while_collapse':card_item.keep_show_while_collapse}
                        ]
                        if card_item.invisible_domain:
                            if row[1]:
                                row[1].update({'invisible_domain':card_item.invisible_domain})
                            row[0].update({'invisible_domain':card_item.invisible_domain})
                        
                        if card_item.font_color:
                            if row[1]:
                                row[1].update({'font_color':card_item.font_color})
                        
                        if len(action_items):
                            if row[1]:
                                row[1].update({'action_buttons':action_items})
                        
                        if card_item.field_ttype == 'selection':
                            field_name = row[1]['name']
                            selection_items = self.env[card_item.model.model].fields_get([field_name])
                            if selection_items and selection_items.get(field_name):
                                selection_items_values = selection_items.get(field_name)
                                if 'selection' in selection_items_values:
                                    row[1].update({'selection_items':selection_items_values['selection']})
                        
                if card_item.sub_position.name == 'side':
                    row[0].update({'local_compute_function':card_item.local_compute_function})
                    if row[1]:
                        row[1].update({'local_compute_function':card_item.local_compute_function})
                    if card_item.local_compute_function not in selected_compute_functions:
                        selected_compute_functions.append(card_item.local_compute_function)

                    side_content.append(row)
                elif card_item.sub_position.name == 'notes_section':
                    row[0].update({'local_compute_function':card_item.local_compute_function})
                    row[1].update({'local_compute_function':card_item.local_compute_function})
                    if card_item.local_compute_function not in selected_compute_functions:
                        selected_compute_functions.append(card_item.local_compute_function)

                    notes_content.append(row)
                else:
                    row[0].update({'local_compute_function':card_item.local_compute_function})
                    if row[1]:
                        row[1].update({'local_compute_function':card_item.local_compute_function})
                    if card_item.local_compute_function not in selected_compute_functions:
                        selected_compute_functions.append(card_item.local_compute_function)
                        
                    content.append(row)
            elif card_item.position=='footer':
                footer.append({
                    "icon": card_item.button_icon,
                    'color': card_item.background,
                    'function_name':card_item.button_function.technical_name,
                    'position':card_item.sub_position,
                    'button_theme':card_item.button_theme,
                    'label': card_item.label,
                    'local_compute_function': card_item.local_compute_function,
                    'invisible_domain':card_item.invisible_domain
                })
                if card_item.is_button:
                        row=[
                            {'name': card_item.label},
                            { 'button': { 'icon': card_item.button_icon, 'text': self.get_field_name(card_item),'function_name':card_item.button_function.technical_name,'label':card_item.label,'is_empty_feild': card_item.is_empty_feild,'title':card_item.title} }
                        ]
                if (card_item and card_item.local_compute_function):
                    selected_compute_functions.append(card_item.local_compute_function)
                if card_item.invisible_domain:
                    row[0].update({'invisible_domain':card_item.invisible_domain})
            if card_item.field_ttype=='many2many' or card_item.field_ttype=='one2many':
                many2many_relations[card_item.field.name] = card_item.field_relation
            
        header['header_bubles'] = header_bubles
        header['header_sub_bubles'] = header_sub_bubles
        names = self.fetch_local_compute_fields(card.fields_names, selected_compute_functions)
        response = {'header':header,'content':content,'footer':footer,'fields_names':names,'many2many_relations':many2many_relations,'theme':card.card_theme}
        if len(side_content)>0 :
            response['side_content']=side_content
        if len(notes_content)>0:
            response['notes_content']=notes_content
        return response
    
    def get_field_name(self,card_item):
        if card_item.field_ttype=='many2one':
            return card_item.field.name+'[1]'
        elif card_item.field_ttype=='many2many' or card_item.field_ttype=='one2many':
            return card_item.field.name+'.length'
        else:
            return card_item.field.name
    
    def fetch_local_compute_fields(self, fields_names, selected_compute_functions):
        names = json.loads(fields_names)
        for key in selected_compute_functions:
            if key in self.LOCAL_COMPUTE_FIELDS:
                value = self.LOCAL_COMPUTE_FIELDS[key]
                for field in value:
                    names.append(field) 
        return names

class rb_delivery_card_sub_position(models.Model):

    _name = 'rb_delivery.card_sub_position'

    parent_position = fields.Char()

    name = fields.Char()

class rb_delivery_card_model_functions(models.Model):

    _name = 'rb_delivery.card_model_functions'
    
    _inherit = 'mail.thread'

    name = fields.Char(track_visibility="on_change")

    technical_name = fields.Char(track_visibility="on_change")

    model = fields.Many2one('ir.model',track_visibility="on_change")

    required_fields = fields.Many2many('ir.model.fields',track_visibility="on_change")
    
    @api.constrains('required_fields')
    def update_card_fields(self):
        card_creators = self.find_related_card_creators()
        for card_creator in card_creators:
            card_creator.compute_card_fields()
    
    def find_related_card_creators(self):
        card_items = self.env['rb_delivery.mobile_card_item'].search([['button_function','=',self.id]])
        card_creators = self.env['rb_delivery.mobile_card_creator'].search([['card_items','in',card_items.ids]])
        if card_creators:
            return card_creators
        return []