import logging
from openerp import models, api
from openerp.exceptions import UserError
from odoo import api, fields, models, tools, SUPERUSER_ID, _
from odoo.tools import config
from collections import OrderedDict
from odoo.sql_db import TestCursor
import os
import tempfile
import requests
import re
from contextlib import closing

_logger = logging.getLogger(__name__)


class ir_actions_report(models.Model):
    _inherit = 'ir.actions.report'

    def _convert_html_links(self, html_content):
        if isinstance(html_content, bytes):
            html_content = html_content.decode('utf-8')
        
        html_content = self._inline_css_and_remove_js(html_content.encode('utf-8')).decode('utf-8')
        
        html_content = re.sub(r'href="(/[^"]*)"', r'href="http://web:8069\1"', html_content)
        
        html_content = re.sub(r'src="(/[^"]*)"', r'src="http://web:8069\1"', html_content)
        
        html_content = re.sub(r"url\('(/[^']*)'\)", r"url('http://web:8069\1')", html_content)
        html_content = re.sub(r'url\("(/[^"]*)"\)', r'url("http://web:8069\1")', html_content)
        
        html_content = re.sub(r'@import "(/[^"]*)"', r'@import "http://web:8069\1"', html_content)
        
        return html_content.encode('utf-8')

    def _inline_css_and_remove_js(self, html_content):
        if isinstance(html_content, bytes):
            html_content = html_content.decode('utf-8')
        
        html_content = self._inline_css_from_files(html_content)

        html_content = re.sub(r'\n\s*\n', '\n', html_content)
        return html_content.encode('utf-8')

    def _inline_css_from_files(self, html_content):
        if isinstance(html_content, bytes):
            html_content = html_content.decode('utf-8')
        
        css_link_pattern = r'<link[^>]*rel=["\']stylesheet["\'][^>]*href=["\']([^"\']*)["\'][^>]*>'
        css_links = re.findall(css_link_pattern, html_content, re.IGNORECASE)
        for css_url in css_links:
            try:
                css_content = None
                
                if '/web/content/' in css_url:
                    css_content = self._get_css_from_database(css_url)
                    
                if css_content:
                    inline_style = f'<style type="text/css">\n/* Inlined from {css_url} */\n{css_content}\n</style>'
                    
                    link_tag_pattern = rf'<link[^>]*href=["\']' + re.escape(css_url) + r'["\'][^>]*>'
                    html_content = re.sub(link_tag_pattern, inline_style, html_content, flags=re.IGNORECASE)
                    
                else:
                    module_static_path = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'src', 'css', 'report.css')
                    module_static_path = os.path.abspath(module_static_path)
                    css_content_from_file = None
                    if os.path.exists(module_static_path):
                        try:
                            with open(module_static_path, 'r', encoding='utf-8') as f:
                                css_content_from_file = f.read()
                        except Exception as read_error:
                            pass
                    if css_content_from_file:
                        inline_style = f'<style type="text/css">\n/* Inlined from {module_static_path} */\n{css_content_from_file}\n</style>'
                        link_tag_pattern = rf'<link[^>]*href=["\']' + re.escape(css_url) + r'["\'][^>]*>'
                        html_content = re.sub(link_tag_pattern, inline_style, html_content, flags=re.IGNORECASE)
                    else:
                        css_url_parts = css_url.lstrip('/').split('/')
                        if css_url_parts:
                            module_name = css_url_parts[0]
                            relative_css_path = os.path.join(*css_url_parts[1:]) if len(css_url_parts) > 1 else ''
                            module_base_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), module_name)
                            css_file_path = os.path.join(module_base_path, relative_css_path)
                            css_content_from_file = None
                            if os.path.exists(css_file_path):
                                try:
                                    with open(css_file_path, 'r', encoding='utf-8') as f:
                                        css_content_from_file = f.read()
                                except Exception as read_error:
                                    pass
                            if css_content_from_file:
                                inline_style = f'<style type="text/css">\n/* Inlined from {css_file_path} */\n{css_content_from_file}\n</style>'
                                link_tag_pattern = rf'<link[^>]*href=["\']' + re.escape(css_url) + r'["\'][^>]*>'
                                html_content = re.sub(link_tag_pattern, inline_style, html_content, flags=re.IGNORECASE)
                            else:
                                link_tag_pattern = rf'<link[^>]*href=["\']' + re.escape(css_url) + r'["\'][^>]*>'
                                html_content = re.sub(link_tag_pattern, f'<!-- CSS link removed: {css_url} -->', html_content, flags=re.IGNORECASE)
                        else:
                            link_tag_pattern = rf'<link[^>]*href=["\']' + re.escape(css_url) + r'["\'][^>]*>'
                            html_content = re.sub(link_tag_pattern, f'<!-- CSS link removed: {css_url} -->', html_content, flags=re.IGNORECASE)
                        
            except Exception as e:
                continue
        
        return html_content

    def _get_css_from_database(self, css_url):
        try:
            css_filename = css_url.split('/')[-1]
            
            attachment = None
            
            attachment = self.env['ir.attachment'].search([
                ('name', 'ilike', f'%{css_filename}%')
            ], limit=1)
            
            if not attachment:
                attachment = self.env['ir.attachment'].search([
                    ('name', '=', css_url)
                ], limit=1)
            
            if not attachment and css_url.startswith('/web/content/'):
                url_parts = css_url.split('/')
                if len(url_parts) >= 4:
                    asset_name = url_parts[-1].split('.')[0]
                    attachment = self.env['ir.attachment'].search([
                        ('name', 'ilike', f'%{asset_name}%'),
                        ('mimetype', 'ilike', 'text/css')
                    ], limit=1)
            
            if not attachment:
                attachment = self.env['ir.attachment'].search([
                    ('name', 'ilike', 'web.assets%'),
                    ('mimetype', 'ilike', 'text/css')
                ], limit=1)
            
            if attachment and attachment.index_content:
                return attachment.index_content
        except Exception as e:
            pass
        
        return None

    def _get_css_from_static_files(self, css_url):
        try:
            static_paths = [
                '/opt/odoo/addons',
                '/var/lib/odoo/addons',
                '/usr/lib/python3/dist-packages/odoo/addons',
                './addons'
            ]
            
            relative_path = css_url.lstrip('/')
            
            for base_path in static_paths:
                full_path = os.path.join(base_path, relative_path)
                if os.path.exists(full_path) and os.path.isfile(full_path):
                    try:
                        with open(full_path, 'r', encoding='utf-8') as f:
                            css_content = f.read()
                            return css_content
                    except Exception as read_error:
                        continue
        except Exception as e:
            pass
        
        return None

    @api.model
    def _run_wkhtmltopdf(
            self,
            bodies,
            header=None,
            footer=None,
            landscape=False,
            specific_paperformat_args=None,
            set_viewport_size=False):
        paperformat_id = self.get_paperformat()
        command_args = self._build_wkhtmltopdf_args(
            paperformat_id,
            landscape,
            specific_paperformat_args=specific_paperformat_args,
            set_viewport_size=set_viewport_size)
        shared_dir = '/var/lib/odoo/pdfs'
        if not os.path.exists(shared_dir):
            os.makedirs(shared_dir, mode=0o755, exist_ok=True)
        files_command_args = []
        temporary_files = []
        if header:
            header = self._convert_html_links(header)
            head_file_fd, head_file_path = tempfile.mkstemp(suffix='.html', prefix='report.header.tmp.', dir=shared_dir)
            with closing(os.fdopen(head_file_fd, 'wb')) as head_file:
                head_file.write(header)
            temporary_files.append(head_file_path)
            files_command_args.extend(['--header-html', head_file_path])
        if footer:
            footer = self._convert_html_links(footer)
            foot_file_fd, foot_file_path = tempfile.mkstemp(suffix='.html', prefix='report.footer.tmp.', dir=shared_dir)
            with closing(os.fdopen(foot_file_fd, 'wb')) as foot_file:
                foot_file.write(footer)
            temporary_files.append(foot_file_path)
            files_command_args.extend(['--footer-html', foot_file_path])
        paths = []
        for i, body in enumerate(bodies):
            body = self._convert_html_links(body)
            prefix = '%s%d.' % ('report.body.tmp.', i)
            body_file_fd, body_file_path = tempfile.mkstemp(suffix='.html', prefix=prefix, dir=shared_dir)
            with closing(os.fdopen(body_file_fd, 'wb')) as body_file:
                body_file.write(body)
            paths.append(body_file_path)
            temporary_files.append(body_file_path)
        pdf_report_fd, pdf_report_path = tempfile.mkstemp(suffix='.pdf', prefix='report.tmp.', dir=shared_dir)
        os.close(pdf_report_fd)
        temporary_files.append(pdf_report_path)
        try:
            command_string = "--enable-local-file-access "
            if shared_dir.startswith('/var/lib/odoo'):
                command_string += "--allow /var/lib/odoo/pdfs "
            else:
                command_string += f"--allow {shared_dir} "
            for arg in command_args:
                command_string += f"{arg} "
            for i in range(0, len(files_command_args), 2):
                if i+1 < len(files_command_args):
                    option = files_command_args[i]
                    path = files_command_args[i+1]
                    if shared_dir.startswith('/var/lib/odoo'):
                        rel_path = os.path.relpath(path, '/var/lib/odoo')
                        command_string += f'{option} file:///var/lib/odoo/{rel_path} '
                    else:
                        command_string += f'{option} file://{path} '
            for path in paths:
                if shared_dir.startswith('/var/lib/odoo'):
                    rel_path = os.path.relpath(path, '/var/lib/odoo')
                    command_string += f'file:///var/lib/odoo/{rel_path} '
                else:
                    command_string += f'file://{path} '
            command_string = 'wkhtmltopdf ' + command_string
            if self._context.get('get_command'):
                payload = {
                    'command_string': command_string
                }
                return payload
            else:
                baseUrl = 'http://wep-api-clusterip'
                payload = {
                    'command_string': command_string
                }
                response = requests.post(
                    f'{baseUrl}/convert-advanced',
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                )
                if response.status_code != 200:
                    error_data = response.json() if response.headers.get('content-type') == 'application/json' else {'error': response.text}
                    error_msg = error_data.get('error', 'Unknown error')
                    details = error_data.get('details', '')
                    raise UserError('Wkhtmltopdf API failed: %s. Details: %s' % (error_msg, details))
                pdf_content = response.content
                return pdf_content, payload 

        except requests.exceptions.RequestException as e:
            raise UserError('Failed to connect to wkhtmltopdf service: %s' % str(e))
        except Exception as e:
            raise UserError('Unexpected error: %s' % str(e))


    @api.model
    def get_wkhtmltopdf_state(self):
        return "ok"

    @api.multi
    def render_qweb_pdf(self, res_ids=None, data=None):
        if not data:
            data = {}
        data.setdefault('report_type', 'pdf')

        data.update(enable_editor=False)

        if (tools.config['test_enable'] or tools.config['test_file']) and not self.env.context.get('force_report_rendering'):
            return self.render_qweb_html(res_ids, data=data)

        context = dict(self.env.context)
        if not config['test_enable']:
            context['commit_assetsbundle'] = True

        context['debug'] = False
        if isinstance(self.env.cr, TestCursor):
            return self.with_context(context).render_qweb_html(res_ids, data=data)[0]

        save_in_attachment = OrderedDict()
        if res_ids:
            Model = self.env[self.model]
            record_ids = Model.browse(res_ids)
            wk_record_ids = Model
            if self.attachment:
                for record_id in record_ids:
                    attachment_id = self.retrieve_attachment(record_id)
                    if attachment_id:
                        save_in_attachment[record_id.id] = attachment_id
                    if not self.attachment_use or not attachment_id:
                        wk_record_ids += record_id
            else:
                wk_record_ids = record_ids
            res_ids = wk_record_ids.ids

        if save_in_attachment and not res_ids:
            _logger.info('The PDF report has been generated from attachments.')
            return self._post_pdf(save_in_attachment), 'pdf'

        if self.get_wkhtmltopdf_state() == 'install':
            raise UserError(_("Unable to find Wkhtmltopdf on this system. The PDF can not be created."))

        html = self.with_context(context).render_qweb_html(res_ids, data=data)[0]

        html = html.decode('utf-8')

        bodies, html_ids, header, footer, specific_paperformat_args = self.with_context(context)._prepare_html(html)

        if self.attachment and set(res_ids) != set(html_ids):
            raise UserError(_("The report's template '%s' is wrong, please contact your administrator. \n\n"
                "Can not separate file to save as attachment because the report's template does not contains the attributes 'data-oe-model' and 'data-oe-id' on the div with 'article' classname.") %  self.name)
        
        pdf_content = self.with_context(get_command=context.get('get_command'))._run_wkhtmltopdf(
            bodies,
            header=header,
            footer=footer,
            landscape=context.get('landscape'),
            specific_paperformat_args=specific_paperformat_args,
            set_viewport_size=context.get('set_viewport_size'),
        )
        return pdf_content
