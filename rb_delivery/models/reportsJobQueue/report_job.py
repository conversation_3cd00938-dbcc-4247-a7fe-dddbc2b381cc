import json
from openerp import models, fields, api, _
from openerp.exceptions import ValidationError, Warning
from unidecode import unidecode
import re
from datetime import timedelta
import os
from odoo.tools import config
import pickle
from odoo.http import request
from datetime import datetime
import base64
import logging
import requests
import json
import psutil
import time


_logger = logging.getLogger(__name__)
class olivery_report_job(models.Model):

    _name = 'rb_delivery.report_job_queue'
    _description = "Report Job"

    reports = {'order_business_report_names':['rb_delivery.collective_report','rb_delivery.collection_print','rb_delivery.partner_money_collection','rb_delivery.returned_collection_per_business_print','rb_delivery.sender_partner_money_collection','rb_delivery.order_finanical_report'],
               'order_agent_report_names':['rb_delivery.agent_money_print','rb_delivery.agent_returned_collection_print','rb_delivery.agent_report_id'],
               'collections_report_names':['rb_delivery.multi_print_orders_money_collector_print_report','rb_delivery.multi_print_orders_partner_money_collection','rb_delivery.receipt_declaration',
                                'rb_delivery.multi_print_orders_money_collector_print_report_bank_detail','rb_delivery.multi_print_orders_money_collector_and_receipt_print_report',
                                'rb_delivery.branch_collection_report','rb_delivery.money_collecion_invoice_receipt','rb_delivery.agent_money_collection','rb_delivery.agent_profit',
                                'rb_delivery.returned_money_collection','rb_delivery.returned_collection_non_financial_report','rb_delivery.agent_returned_collection','rb_delivery.returned_receipt','rb_delivery.runsheet','rb_delivery.money_collection_finanical_report','rb_delivery.company_profit','rb_delivery.agent_report',
                                'rb_delivery.money_collection_receipt_a4','rb_delivery.money_collection_receipt_a5','rb_delivery.money_collection_receipt_a6','rb_delivery.agent_profit_report','rb_delivery.returned_money_collection_with_reason','rb_delivery.agent_report_with_services', 'rb_delivery.money_collection_with_services', 'rb_delivery.driver_receipt'],
                'collection_business_report_names':['rb_delivery.money_collection_total_receipt_A4']}


    name = fields.Char(string='Name', required=True)

    report_data = fields.Binary(string='Report Data')

    report_name = fields.Char(string='Report Name')

    reference = fields.Char(string='Reference')

    active_ids = fields.Text(string="Active IDs")

    user = fields.Many2one(
        'rb_delivery.user', 'User',track_visibility="on_change",required=False)

    print_date_time = fields.Datetime(string='Print Date Time')


    model = fields.Char('Model')

    state = fields.Selection([('printed','Printed'),('unprinted','Unprinted')],'Print Status',default='unprinted',track_visibility="on_change") 

    def generate_report(self, report_name, data, sessionID, baseUrl,model):
        reportData = self.with_delay(channel='root.report', max_retries=2).generate_report_async(report_name ,data, sessionID, baseUrl,model).uuid
        return reportData
        


    def generate_report_async(self,report_name, data,sessionID, baseUrl,model):
        active_ids = json.dumps(data)
        import requests
        data = str(data)
        data = data[1:-1]
        baseUrl = 'http://web:8069'
        url = baseUrl+'/report/pdf/'+report_name +'/' + data
        try:
            cookies = {'session_id': sessionID}
            response = requests.get(url, cookies=cookies)
            user = self.env['rb_delivery.user'].sudo().search([('user_id', '=', self._uid)])
            import uuid
            id = str(uuid.uuid4())
            report = self.env['ir.actions.report'].search([
                ('report_name', '=', report_name),
            ]).sudo()
            self.env['rb_delivery.report_job_queue'].sudo().create({'name': report.name, 'report_data': base64.b64encode(response.content), 'report_name': report_name, 'reference': id,'active_ids':active_ids,'model':model,
            'user': user.id,
            'print_date_time': datetime.now()
            })
            return id
        except Exception as e:
            _logger.info("#################ERROR#################")
            _logger.info(e)
            _logger.info("#################ERROR#################")
            return 'Error'
    

    def check_report_status(self, job_id, url):
        job = self.env['queue.job'].sudo().search([('uuid', '=', job_id)], limit=1)
        if not job:
            return "Job not found"
        if job.state == 'failed':
            return "Printing failed"
        if job.state == 'done':
            report_res = self.env['rb_delivery.report_job_queue'].sudo().search([('reference', '=', job.result)], limit=1)
            
            if report_res and report_res.report_data:
                if isinstance(report_res.report_data, bool):
                    return 'Printing failed'
                baseUrl = url+'/download/report/' + job.result
                report_res.write({'state':'printed'})
                return baseUrl
        return job.state
    
    @api.model
    def compute_totals(self,report_name,docids):
        orders_limit = int(request.env['rb_delivery.client_configuration'].sudo().search([('key', '=', 'orders_qweb_limit')], limit=1).text)
        collections_limit = int(request.env['rb_delivery.client_configuration'].sudo().search([('key', '=', 'collections_qweb_limit')], limit=1).text)
        data = {}
        if report_name in self.reports['order_business_report_names']:
            business_lists = self.env['rb_delivery.order'].get_doc_ids(docids,'business')
            for business_list in business_lists:
                groups = self.split_into_groups(business_list,orders_limit)
                current_offset = 0
                data = self.env['rb_delivery.utility'].get_meta_data(business_list,report_name)
                data['data_length'] = len(business_list.split(','))
                for group in groups:
                    data['offset'] = current_offset
                    current_offset += len(group.split(','))
        elif report_name in self.reports['order_agent_report_names']:
            agent_lists=self.env['rb_delivery.order'].get_doc_ids(docids,'agent')
            for agent_list in agent_lists:
                groups = self.split_into_groups(agent_list,orders_limit)
                current_offset = 0
                data = self.env['rb_delivery.utility'].get_meta_data(agent_list,report_name)
                data['data_length'] = len(agent_list.split(','))
                for group in groups:
                    data['offset'] = current_offset
                    current_offset += len(group.split(','))

        elif report_name in self.reports['collections_report_names']:
            docids_list=docids.split(",")
            current_offset = 0
            for group in docids_list:
                data = self.env['rb_delivery.utility'].get_meta_data(group,report_name)
                data['data_length'] = len(group.split(','))
                data['offset'] = current_offset
                current_offset += len(group.split(','))

        elif report_name in self.reports['collection_business_report_names']:
            business_lists = self.env['rb_delivery.multi_print_orders_money_collector'].get_collection_doc_ids(docids,'business')
            current_offset = 0
            for business_list in business_lists:

                groups = self.split_into_groups(business_list,collections_limit)
                for group in groups:
                    data = self.env['rb_delivery.utility'].get_meta_data(group,report_name)
                    data['data_length'] = len(group.split(','))
                    data['offset'] = current_offset
                    current_offset += len(group.split(','))

        elif report_name == "rb_delivery.multi_print_driver_receipt_report":

            agent_lists=self.env['rb_delivery.multi_print_orders_money_collector'].get_collection_doc_ids(docids,'driver')
            current_offset = 0

            for agent_list in agent_lists:
                agent_offset = 0
                groups = self.split_into_groups(agent_list,collections_limit)
                data = self.env['rb_delivery.utility'].get_meta_data(agent_list,report_name)
                data['agent_collection_length'] = len(agent_list.split(','))

                for group in groups:
                    data['agent_offset'] = agent_offset
                    data['data_length'] = len(group.split(','))
                    data['offset'] = current_offset
                    current_offset += len(group.split(','))
                    agent_offset += collections_limit

        else:
            groups = self.split_into_groups(docids,orders_limit)
            current_offset = 0
            data = self.env['rb_delivery.utility'].get_meta_data(docids,report_name)
            data['data_length'] = len(docids.split(','))
            for group in groups:
                data['offset'] = current_offset
                current_offset += len(group.split(','))
        return data
    
    def split_into_groups(self,numbers, group_size):
        numbers_list = numbers.split(',')
        groups = [numbers_list[i:i+group_size] for i in range(0, len(numbers_list), group_size)]
        groups_str = [','.join(group) for group in groups]
        return groups_str

    @api.model
    def generate_mobile_pdf_report(self, report_name, model_name, records_ids, context):
        context_dic = json.loads(context)
        data = self.compute_totals(report_name,','.join(map(str, records_ids)))
        headers = {
            'Content-Type': 'application/json'
        }
        base_url = 'http://web:8069/report_mobile/pdf'
        context_dic = json.loads(context)
        report_data = {
            'context': context,
            'report': report_name,
            'model': model_name,
            'records_ids': records_ids,
            'data': data
        }
        report = self.env['ir.actions.report'].search([
                ('report_name', '=', report_name),
            ]).sudo()
        response = requests.post(base_url, headers=headers, json=report_data)
        if response.content:
            response_str = response.content.decode('utf-8')
            data = json.loads(response_str)
            report_data_bytes = str(data['result'])

            user = self.env['rb_delivery.user'].sudo().search([('user_id', '=', context_dic.get('uid'))])
            import uuid
            id = str(uuid.uuid4())
            self.env['rb_delivery.report_job_queue'].sudo().create({
                'name': report.name, 
                'report_data': (report_data_bytes), 
                'report_name': report.name, 
                'reference': id, 
                'user': user.id,
                'print_date_time': datetime.now()
            })

            return (report_data_bytes)


    @api.model
    def get_mobile_pdf_result(self, jq_id):
        job = self.env['queue.job'].sudo().search([('uuid', '=', jq_id)], limit=1)
        if not job:
            return "Job not found"
        if job.state == 'failed':
            return "Printing failed"
        if job.state == 'done':
            pdf = job.result
            if pdf:
                if isinstance(pdf, bool):
                    return _('Printing failed')
                return pdf
        return job.state
    
    @api.model
    def search_read(self, domain, fields, offset=0, limit=None, order=None):
        user = self.env['rb_delivery.user'].sudo().search([('user_id', '=', self._uid)])
        domain = [('user', '=', user.id)] + domain
        return super(olivery_report_job, self).search_read(domain, fields, offset, limit, order)
    