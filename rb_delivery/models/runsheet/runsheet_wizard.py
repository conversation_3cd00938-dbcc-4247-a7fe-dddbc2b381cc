# -*- coding: utf-8 -*-
from openerp import models, fields, api,_
from openerp.exceptions import ValidationError
from odoo.exceptions import AccessError, UserError, RedirectWarning, ValidationError, Warning
from odoo.exceptions import UserError
from openerp import SUPERUSER_ID

from odoo.osv import osv
class order_create_runsheet_wizard(models.TransientModel):
    _name = 'rb_delivery.create_runsheet'
    _description = "Runsheet wizard"

    #Inherited in:
    #olivery_web_barcode
    def create_runsheet(self,order_ids,show_exist_wizard=True):
        if isinstance(order_ids, list) and len(order_ids)>0:
            recs = self.env['rb_delivery.order'].sudo().browse(order_ids)
        elif 'active_ids' in order_ids and order_ids['active_ids']:
            recs = self.env['rb_delivery.order'].browse(order_ids['active_ids'])
        existing_records = []

        if not recs:
            raise UserError(_("No orders selected for runsheet creation."))

        orders_by_agent = {}
        for order in recs:
            agent = order.sudo().assign_to_agent
            agent_key = agent.id if agent else False
            if agent_key not in orders_by_agent:
                orders_by_agent[agent_key] = self.env['rb_delivery.order']
            orders_by_agent[agent_key] |= order

        allow_many_runsheet = self.env['rb_delivery.client_configuration'].get_param('allow_many_runsheet')
        overall_actions = []

        for agent_id, orders in orders_by_agent.items():
            existing_records = []
            for order in orders:
                if order.sudo().runsheet_collection_id:
                    existing_records.append(
                        _('This order %s is in distribution collection of sequence %s') % (
                            order.sequence, order.sudo().runsheet_collection_id.sequence
                        )
                    )

            exist_message = "\n".join(existing_records)
            values = {'order_ids': orders.ids}
            if self.name:
                values['name'] = self.name
            if agent_id:
                values['agent_id'] = agent_id

            if existing_records:
                if not allow_many_runsheet:
                    self.env['rb_delivery.error_log'].raise_olivery_error(291, self.id, {'exist_message': _(exist_message)})
                    #raise ValidationError(_(exist_message))
                elif show_exist_wizard:
                    # Show a dialog with the existing runsheet information
                    runsheet_dialog = self.env['display.runsheet_dialog.box'].sudo().create({'message': exist_message})
                    context = values
                    action = {
                        'type': 'ir.actions.act_window',
                        'name': _('Message'),
                        'res_model': 'display.runsheet_dialog.box',
                        'view_type': 'form',
                        'view_mode': 'form',
                        'target': 'new',
                        'res_id': runsheet_dialog.id,
                        'context': context
                    }
                    overall_actions.append(action)
                else:
                    runsheet = self.env['rb_delivery.runsheet'].create(values)
                    action = self._generate_report_action(runsheet)
                    overall_actions.append(action)
            else:
                runsheet = self.env['rb_delivery.runsheet'].create(values)
                action = self._generate_report_action(runsheet)
                overall_actions.append(action)

        if overall_actions:
            return overall_actions[0]
        return True

    def _generate_report_action(self, runsheet):
        print_pdf = self.env['rb_delivery.client_configuration'].get_param('print_runsheet_pdf_on_create')
        if runsheet and print_pdf:
            context = {
                'active_ids': [runsheet.id],
                'active_model': 'rb_delivery.runsheet'
            }
            return {
                'type': 'ir.actions.report',
                'report_type': 'qweb-pdf',
                'report_name': 'rb_delivery.runsheet',
                'context': context,
                'active_ids': [runsheet.id],
                'report_file': 'rb_delivery.runsheet',
            }
        return True

    @api.model
    def _default_show_note(self):
        default_show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        return default_show_note
    @api.one
    def _compute_show_note(self):
        show_note = self.env['rb_delivery.client_configuration'].get_param('show_note_in_collections')
        self.show_note = show_note

    name = fields.Char('Note')
    show_note = fields.Boolean('Show Note', default=_default_show_note, compute="_compute_show_note", readonly=True)
class display_runsheet_dialog_box(osv.osv):
    _name = "display.runsheet_dialog.box"

    message = fields.Text('Message', translate=False)
    @api.multi
    def create_runsheet(self):
        order_ids = self._context.get('order_ids')
        name = self._context.get('name')
        values = {}

        if order_ids:
            orders = self.env['rb_delivery.order'].browse(order_ids)
            values['order_ids']=orders.ids
        if name:
            values['name'] = name
        if values:
           report = self.env['rb_delivery.runsheet'].create(values)
        print_pdf = self.env['rb_delivery.client_configuration'].get_param('print_runsheet_pdf_on_create')
        if print_pdf:
            context = {
                'active_ids': report.id
            }
            action = {
                'type': 'ir.actions.report',
                'report_type': 'qweb-pdf',
                'active_ids': report.id,
                'context': context,
                'report_name': 'rb_delivery.runsheet',
                'report_file': 'rb_delivery.runsheet',
            }
            return action
        return True

display_runsheet_dialog_box()
class runsheet_collection_change_agent_wizard(models.TransientModel):
    _name = 'rb_delivery.runsheet_collection_change_agent_wizard'
    _description = "Runsheet Collection Change Agent wizard Model"

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    @api.model
    def default_driver(self):
        user = self.env['res.users'].search([('id', '=', self._uid)])
        is_driver = user.has_group('rb_delivery.role_driver')
        if is_driver:
            del_user = self.env['rb_delivery.user'].search(
                [('user_id', '=', user.id)])
            return del_user.id
        else:
            return False
    assign_to_agent = fields.Many2one(
        'rb_delivery.user', 'Agent', domain=_get_driver_users, default=default_driver)

    @api.multi
    def select_agent(self):
        collections = self.env['rb_delivery.runsheet'].browse(
            self._context.get('active_ids'))

        for collection in collections:
            collection.write({'assign_to_agent':self.assign_to_agent.id})
            recs = collection.order_ids
            for rec in recs:
                rec.write({'assign_to_agent': self.assign_to_agent.id})

        return True

class rb_delivery_runsheet_detach_order(models.TransientModel):
    _name="rb_delivery.runsheet_detach_order"
    _description = "Runsheet Detach Order Model"

    @api.multi
    def detach_orders(self):
        recs = self.env['rb_delivery.runsheet'].browse(
            self._context.get('active_ids'))
        for rec in recs:
            rec.write({'order_ids':[(6,0,[])]})
        return True

class order_select_runsheet_state_wizard(models.TransientModel):
    _name = 'rb_delivery.select_runsheet_state'
    _description = "Select Runsheet State Model"

    def _get_driver_users(self):
        ids = []
        group = self.env.ref('rb_delivery.role_driver')
        users = self.env['rb_delivery.user'].search([('group_id', '=', group.id)])
        for user in users:
            ids.append(user.id)
        return [('id', 'in', ids)]

    state = fields.Selection(selection='get_status',track_visibility="on_change",string="Status",default="")
    agent = fields.Many2one( 'rb_delivery.user', 'Agent', track_visibility="on_change", domain=_get_driver_users)
    required_agent = fields.Boolean('Show Agent Required', default=False)
    show_agent = fields.Boolean('Show Agent', default=False)

    def get_status(self):
        group_id = self.env['rb_delivery.user'].search([('user_id', '=', self._uid)]).group_id
        next_statuses=self.env['rb_delivery.status'].search([('status_type','=','olivery_collection'),('collection_type','=','runsheet_collection')])
        status_list =[]
        for status in next_statuses:
            if group_id:
                if status.role_action_status_ids and len(status.role_action_status_ids)>0:
                    for role in status.role_action_status_ids:
                        if role.id == group_id.id:
                            status_list.append((status.name,status.title))
            else:
                status_list.append((status.name,status.title))
        return status_list

    # inherit module[olivery_branch_collection]
    @api.onchange('state')
    def change_state(self):
        state = self.env['rb_delivery.status'].sudo().search([('name','=',self.state),('status_type','=','olivery_collection'),('collection_type','=','runsheet_collection')])
        optional_status_actions = state.status_action_optional_related_fields
        required_status_actions = state.status_action_required_aditional_fields
        self.show_agent = False
        self.required_agent = False
        self.agent=False
        if state and optional_status_actions:
            for status_action in optional_status_actions:
                if status_action.name == 'collection_show_agent':
                    self.show_agent = True
        if state and required_status_actions:
            for status_action in required_status_actions:
                if status_action.name == 'collection_show_agent':
                    self.show_agent = True
                    self.required_agent = True

    # inherit module[olivery_branch_collection]
    @api.multi
    def select_state(self):
        state = self.env['rb_delivery.status'].sudo().search([('name','=',self.state),('status_type','=','olivery_collection'),('collection_type','=','runsheet_collection')])
        runsheet_ids = self.env['rb_delivery.runsheet'].browse(
            self._context.get('active_ids'))
        runsheet_collection_vals = {}
        order_vals = {}
        if self.state:
            runsheet_collection_vals['state'] = self.state
            if state.related_order_status:
                order_state = self.env['rb_delivery.status'].sudo().search([('name','=',state.related_order_status),'|',('status_type','=',False),('status_type','=','olivery_order')])
                if order_state:
                    order_vals['state'] = order_state.name
                    order_vals['is_from_collection'] = True
        if self.agent:
            runsheet_collection_vals['assign_to_agent'] = self.agent.id
            order_vals['assign_to_agent'] = self.agent.id
        if runsheet_collection_vals:
            for runsheet_colelction_id in runsheet_ids:
                runsheet_colelction_id.write(runsheet_collection_vals)
                if order_vals and runsheet_colelction_id.order_ids:
                    runsheet_colelction_id.order_ids.write(order_vals)
        return True