odoo.define('rb_delivery.ConfirmDialog', function(require) {
    'use strict';

    var Dialog = require('web.Dialog');
    var core = require('web.core');

    var ConfirmDialog = Dialog.extend({
        init: function(parent, options) {
            this._super(parent, options);
        },
        start: function() {
            this._super();
            this.$modal.find('.modal-header button.close').hide();
        },
    });

    return ConfirmDialog;
});

odoo.define('rb_delivery.report_generator', function(require) {
    "use strict";

    var ActionManager = require('web.ActionManager');
    ActionManager.include({
        _handleAction: function (action, options) {
            if (action.type === 'ir.actions.act_multi') {
                return this._executeMultiAction(action, options);
            }

            return this._super.apply(this, arguments);
        },

        /**
         * Handle 'ir.actions.act_multi' action
         * @param {Object} action see doAction() parameters
         * @param {Object} options see doAction() parameters
         * @param {integer|undefined} index Index of action being handled
         * @returns {$.Promise}
         */
        _executeMultiAction: function (action, options, index) {
            var self = this;

            if (index === undefined) {
                index = 0; // eslint-disable-line no-param-reassign
            }

            if (index === action.actions.length - 1) {
                return this.doAction(action.actions[index], options);
            } else if (index >= action.actions.length) {
                return $.when();
            }

            return this
                .doAction(action.actions[index], options)
                .then(function () {
                    return self._executeMultiAction(action, options, index + 1);
                });
        },
        _executeReportAction: async function(action, options) {
            var self = this;
            if (action.report_type === 'qweb-pdf') {
                try {

                    var activeIds = action.context.active_ids
                    if (activeIds == undefined && action.activeIds)
                        activeIds = action.activeIds

                    var strWindowFeatures = "location=yes,height=1280,width=720,scrollbars=yes,status=yes";
                    var url_out_of_jq = window.location.origin + "/report/pdf/"+ action.report_name +'/'+ activeIds.join(',');
                    window.open(url_out_of_jq, "_blank", strWindowFeatures);
                    self.reloadCurrentView()

                } catch (error) {
                    return this._super.apply(this, arguments);
                }
            } else {
                return this._super.apply(this, arguments);
            }

        },

        reloadCurrentView: function () {
            var currentController = this.getCurrentController();
            if (currentController && currentController.widget && currentController.widget.controller) {
                currentController.widget.controller.reload().then(function () {
                    console.log("View reloaded successfully");
                }).fail(function () {
                    console.error("Failed to reload the view");
                });
            } else if (currentController && currentController.widget && currentController.widget.reload) {
                currentController.widget.reload().then(function () {
                    console.log("View reloaded successfully");
                }).fail(function () {
                    console.error("Failed to reload the view");
                });
            } else {
                console.error("No current controller found or reload method is not defined.");
            }
        }
    });



});



odoo.define('rb_delivery.ReportActionManager', function(require) {
    'use strict';

    var reportActionManager = require('web.ReportActionManager');

    reportActionManager.include({
        _executeReportAction: function (action, options) {
            var self = this;

            if (action.report_type === 'qweb-html') {
                return this._executeReportClientAction(action, options);
            } else if (action.report_type === 'qweb-pdf') {
                return self._triggerDownload(action, options, 'pdf');
            } else if (action.report_type === 'qweb-text') {
                return self._triggerDownload(action, options, 'text');
            } else {
                console.error("The ActionManager can't handle reports of type " +
                    action.report_type, action);
                return $.Deferred().reject();
            }
        }
    });

    return ConfirmDialog;
});
