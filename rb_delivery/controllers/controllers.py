# -*- coding: utf-8 -*-

import odoo
from odoo.http import request,route
from openerp.http import request, Response
from openerp import http,_
from openerp.addons.auth_signup.controllers.main import AuthSignupHome
from odoo.addons.web.controllers.main import Home as WebHome
from openerp.addons.web.controllers.main import ensure_db,ReportController,Export,ExcelExport
from openerp.addons.website.controllers.main import Website
from odoo.exceptions import ValidationError, UserError
from datetime import datetime,timedelta
import logging
import base64
import json
import random
import string
import io
import PyPDF2
from odoo.http import content_disposition
import xlsxwriter
from io import BytesIO
_logger = logging.getLogger(__name__)
import threading
from odoo.modules.module import get_module_resource
from io import BytesIO
import PyPDF2
from odoo import _
from odoo.api import Environment
from odoo.api import call_kw

import re
# Patch fix for samesite issue and secure
from odoo.http import Root
import ast
from odoo.tools import frozendict


original_get_response=Root.get_response
def get_response(self, httprequest, result, explicit_session):
            # print (result)
            # print (httprequest)
            result=original_get_response(self,httprequest,result,explicit_session)
            try:
                cookie=result.headers['Set-Cookie']
                cookie=cookie+";SameSite=None; Secure"
                result.headers['Set-Cookie']=cookie
                return result

            except Exception:
                return result


Root.get_response=get_response

class WebsiteInherit(Website):

    @http.route(website=True, auth="public")
    def web_login(self, redirect=None, *args, **kw):
        response = super(WebsiteInherit, self).web_login(redirect=redirect, *args, **kw)
        if request.params['login_success']:
            res_user = request.env['res.users'].browse(request.uid)
            if res_user:
                delivery_user = request.env['rb_delivery.user'].sudo().search([('user_id','=',res_user.id)])
                if delivery_user:
                    general_configuration = request.env['rb_delivery.general_configuration'].sudo().search([])
                    action = None

                    if delivery_user and general_configuration and general_configuration.redirect_group_ids:
                        redirect_menu = general_configuration.redirect_group_ids.filtered(lambda r: r.group_id.id == delivery_user.group_id.id)
                        if redirect_menu and redirect_menu.redirect_menu_item and redirect_menu.redirect_menu_item.action:
                            action = redirect_menu.redirect_menu_item.action.id
                    if not action:
                        action = request.env.ref('rb_delivery.action_rb_delivery_order').id
                else:
                    action = request.env.ref('rb_delivery.action_rb_delivery_order').id
                redirect = "/web#action="+str(action)

                return http.redirect_with_hash(redirect)
        return response



class WebInherit(AuthSignupHome):

    def get_auth_signup_config(self):
        """retrieve the module config (which features are enabled) for the login page"""

        get_param = request.env['ir.config_parameter'].sudo().get_param
        return {
            'signup_enabled': request.env['res.users']._get_signup_invitation_scope() == 'b2c',
            'reset_password_enabled': get_param('auth_signup.reset_password') == 'False',
        }


    @http.route()
    def web_login(self, *args, **kw):
        # need to inherit because of redirect to orders tab
        ensure_db()
        user_forbidden = False
        if 'password' in kw and kw['password'] and 'login' in kw and kw['login']:
            # added it before call super to change credentials to False
            delivery_user = request.env['rb_delivery.user'].sudo().search(['|',('email','=',kw['login']),('mobile_number','=',kw['login']),('password','=',kw['password'])])
            if delivery_user:
                forbidden_roles_ids = request.env['rb_delivery.client_configuration'].sudo().get_param('determine_web_platform_login')
                if forbidden_roles_ids and len(forbidden_roles_ids) > 0 and delivery_user.group_id and delivery_user.group_id.id in forbidden_roles_ids :
                    request.params['login_success'] = False
                    request.params['login'] = False
                    request.params['password'] = False
                    user_forbidden = True

        response = super(WebInherit, self).web_login(*args, **kw)

        if request.session.sid:
            response.set_cookie('session_id', request.session.sid)

        if 'login' in response.qcontext and 'password' in response.qcontext:

            if user_forbidden :
                response.qcontext.update({'error':_('You are Prevented from logging in on the web platform, please contact your administrator.')})

            else:
                user = None
                account_number_registration = request.env['rb_delivery.client_configuration'].sudo().get_param('account_number_registration')
                email_registration = request.env['rb_delivery.utility'].sudo().check_text_characters_or_numbers(response.qcontext.get('login'))
                if email_registration:
                    user = request.env['rb_delivery.user'].sudo().search([('email','=',response.qcontext.get('login')),('password','=',response.qcontext.get('password'))])
                else:
                    if account_number_registration:
                        user = request.env['rb_delivery.user'].sudo().search([('user_sequence','=',response.qcontext.get('login'))])
                    else:
                        user = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',response.qcontext.get('login'))])
                if not user:
                    if account_number_registration:
                        response.qcontext.update({'error': _('Account number is not registered in the system.')})
                    elif email_registration:
                        response.qcontext.update({'error': _('Email is not registered in the system.')})
                    else:
                        response.qcontext.update({'error': _('Mobile number is not registered in the system.')})

                elif user and user.state!='confirmed' and user.state!='reconfirmed':
                    response.qcontext.update({'error':_('Your account is not confirmed yet, please contact your administrator.')})

        return response


class excelExport(ExcelExport):
    @http.route('/web/export/xls', type='http', auth="user")
    def index(self, data, token):
        data = json.loads(data)
        fields = data['fields']
        if request._uid != 1 and request._uid !=2:
            user = request.env['rb_delivery.user'].sudo().search([('user_id','=',request._uid)])
            if user:
                now_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                user.sudo().write({'export_date':now_date})
                user.message_post(body=_("User has exported from model %s") % (data['model']))
                if not user.role_code == 'rb_delivery.role_super_manager':

                    for field in fields:
                        if field['name'] == 'previous_agent':
                            previous_agent_index = fields.index(field)
                            del fields[previous_agent_index]
                        elif field['name'] == 'extra_agent_cost':
                            extra_agent_cost_index = fields.index(field)
                            del fields[extra_agent_cost_index]
                        elif field['name'] == 'current_branch':
                            current_branch_index = fields.index(field)
                            del fields[current_branch_index]

        data = json.dumps(data)
        response = super(excelExport, self).index(data,token)
        return response

class exportAction(Export):
    @http.route('/web/export/get_fields', type='json', auth="user")
    def get_fields(self, model, prefix='', parent_name= '',import_compat=True, parent_field_type=None, parent_field=None, exclude=None):
        response = super(exportAction, self).get_fields(model, prefix, parent_name,import_compat, parent_field_type,parent_field, exclude)
        index = 0
        company_password_index = ''
        password_index = ''
        user = request.env['rb_delivery.user'].search([('user_id','=',request._uid)], limit=1)
        is_block_delivery = user.block_delivery_fee
        is_block_delivery_profit = user.block_delivery_profit
        delivery_profit_fields = ['delivery_profit', 'required_to_company', 'agent_cost']
        disallowed_roles = ['rb_delivery.role_business', 'rb_delivery.role_driver']
        for res in response:
            if model == 'rb_delivery.order':
                if res['id'] == 'delivery_cost' and is_block_delivery:
                    response.pop(index)
                if res['id'] in delivery_profit_fields and (user.role_code in disallowed_roles or is_block_delivery_profit):
                    response.pop(index)
            if model == 'rb_delivery.user':
                if res['id'] == 'password' or res['id'] == 'assign_to_business/password':
                    password_index = str(index)
                if res['id'] == 'company_password' or res['id'] == 'assign_to_business/company_password':
                    company_password_index = str(index)
            index = index + 1
        if model == 'rb_delivery.user':
            if company_password_index:
                response.pop(int(company_password_index))
            if password_index:
                response.pop(int(password_index))
        return response

class reportAction(ReportController):

    reports = {'order_business_report_names':['rb_delivery.collective_report','rb_delivery.collection_print','rb_delivery.partner_money_collection','rb_delivery.returned_collection_per_business_print','rb_delivery.sender_partner_money_collection','rb_delivery.order_finanical_report','rb_delivery.collection_with_note_print'],
               'order_agent_report_names':['rb_delivery.agent_money_print','rb_delivery.agent_returned_collection_print','rb_delivery.agent_report_id','rb_delivery.dist','rb_delivery.dist_driver'],
               'collections_report_names':['rb_delivery.multi_print_orders_money_collector_print_report','rb_delivery.multi_print_orders_partner_money_collection','rb_delivery.receipt_declaration',
                                'rb_delivery.multi_print_orders_money_collector_print_report_bank_detail','rb_delivery.multi_print_orders_money_collector_and_receipt_print_report',
                                'rb_delivery.branch_collection_report','rb_delivery.money_collecion_invoice_receipt','rb_delivery.agent_money_collection','rb_delivery.agent_profit',
                                'rb_delivery.returned_money_collection','rb_delivery.returned_collection_non_financial_report','rb_delivery.agent_returned_collection','rb_delivery.returned_receipt','rb_delivery.money_collection_finanical_report','rb_delivery.company_profit','rb_delivery.agent_report',
                                'rb_delivery.money_collection_receipt_a4','rb_delivery.money_collection_receipt_a5','rb_delivery.multi_partner_collection_detailed','rb_delivery.money_collection_receipt_a6','rb_delivery.agent_profit_report','rb_delivery.returned_money_collection_with_reason','rb_delivery.agent_report_with_services', 'rb_delivery.money_collection_with_services', 'rb_delivery.driver_receipt', 'rb_delivery.multi_print_orders_money_collector_print_with_note_report','rb_delivery.returned_money_collection_with_barcode','rb_delivery.runsheet','rb_delivery.runsheet_report_without_barcode'],
                'collection_business_report_names':['rb_delivery.money_collection_total_receipt_A4'],
                'runsheet_agent_report_names': ['rb_delivery.runsheet', 'rb_delivery.runsheet_report_without_barcode']}



    def split_into_groups(self,numbers, group_size):
        numbers_list = numbers.split(',')
        groups = [numbers_list[i:i+group_size] for i in range(0, len(numbers_list), group_size)]
        groups_str = [','.join(group) for group in groups]
        return groups_str
    #inherit module [olivery_web_barcode]
    @http.route([
        '/report_custom/<converter>/<reportname>',
        '/report_custom/<converter>/<reportname>/<docids>',
    ], type='http', auth='user')
    def report_custom_routes(self, reportname, docids=None, converter=None, **data):
        if 'offset' in data:
            current_offset = data['offset']
        else:
            current_offset = 0
        ids = [int(x) for x in docids.split(',')]
        model = request.env['ir.actions.report'].sudo().search([('report_name','=',reportname)],limit=1)
        model_name = model.model
        report_name = model.name
        day = datetime.now().strftime('%Y-%m-%d %H:%M')
        user = request.env['res.users'].sudo().browse(request._uid)
        lang = user.lang
        request.env.context = dict(request.env.context)
        request.env.context.update({'lang': lang})
        records = request.env[model_name].browse(ids)
        sequences = []
        for record in records.sudo():
            if hasattr(record, 'sequence'):
                sequences.append(record.sequence)
            else:
                sequences.append(str(record.id))
            try:
                record.message_post(body=_("%s Report was created at %s by %s") %(report_name,day,user.name))
            except:
                pass
        
        import os        
        shared_dir = '/var/lib/odoo/pdfs'
        if not os.path.exists(shared_dir):
            os.makedirs(shared_dir, mode=0o755, exist_ok=True)
        
        command_list = []
        report = request.env['ir.actions.report']._get_report_from_name(reportname)
        if model.report_name in self.reports['order_business_report_names']:
            business_lists = request.env['rb_delivery.order'].get_doc_ids(docids,'business')
            for business_list in business_lists:
                business_list_ids = [int(i) for i in business_list.split(',')]
                data = request.env['rb_delivery.utility'].get_meta_data(business_list,model.report_name)
                data['data_length'] = len(business_list_ids)
                data['offset'] = 0
                context = data.get('context') or dict(request.env.context)
                context['get_command'] = True
                response = report.with_context(context).render_qweb_pdf(business_list_ids,data)
                command_list.append(response.get('command_string'))
        elif model.report_name in self.reports['order_agent_report_names']:
            agent_lists=request.env['rb_delivery.order'].get_doc_ids(docids,'agent')
            for agent_list in agent_lists:
                agent_list_ids = [int(i) for i in agent_list.split(',')]
                data = request.env['rb_delivery.utility'].get_meta_data(agent_list,model.report_name)
                data['data_length'] = len(agent_list_ids)
                data['offset'] = 0
                context = data.get('context') or dict(request.env.context)
                context['get_command'] = True
                response = report.with_context(context).render_qweb_pdf(agent_list_ids,data)
                command_list.append(response.get('command_string'))
        elif model.report_name in self.reports['collections_report_names']:
            docids_list=docids.split(",")
            current_offset = 0
            for group in docids_list:
                data = request.env['rb_delivery.utility'].get_meta_data([group],model.report_name)
                data['data_length'] = len(group.split(','))
                data['offset'] = current_offset
                context = data.get('context') or dict(request.env.context)
                context['get_command'] = True
                response = report.with_context(context).render_qweb_pdf(int(group),data)
                command_list.append(response.get('command_string'))
                current_offset += len(group.split(','))
        elif model.report_name in self.reports['collection_business_report_names']:
            business_lists = request.env['rb_delivery.multi_print_orders_money_collector'].get_collection_doc_ids(docids,'business')
            current_offset = 0
            for business_list in business_lists:
                business_list_ids = [int(i) for i in business_list.split(',')]
                data = request.env['rb_delivery.utility'].get_meta_data(business_list,model.report_name)
                data['data_length'] = len(business_list_ids)
                data['offset'] = current_offset
                context = data.get('context') or dict(request.env.context)
                context['get_command'] = True
                response = report.with_context(context).render_qweb_pdf( business_list_ids,data)
                command_list.append(response.get('command_string'))
                current_offset += len(business_list.split(','))
        elif model.report_name == "rb_delivery.multi_print_driver_receipt_report":
            agent_lists=request.env['rb_delivery.multi_print_orders_money_collector'].get_collection_doc_ids(docids,'driver')
            current_offset = 0
            for agent_list in agent_lists:
                agent_list_ids = [int(i) for i in agent_list.split(',')]
                data = request.env['rb_delivery.utility'].get_meta_data(agent_list,model.report_name)
                data['agent_collection_length'] = len(agent_list_ids)
                data['data_length'] = len(agent_list_ids)
                data['offset'] = current_offset
                context = data.get('context') or dict(request.env.context)
                context['get_command'] = True
                response = report.with_context(context).render_qweb_pdf(agent_list_ids,data)
                command_list.append(response.get('command_string'))
                current_offset += len(agent_list.split(','))
        elif model.report_name in self.reports['runsheet_agent_report_names']:
            agent_lists=request.env['rb_delivery.runsheet'].get_collection_doc_ids(docids,'driver')
            current_offset = 0
            for agent_list in agent_lists:
                agent_list_ids = [int(i) for i in agent_list.split(',')]
                data = request.env['rb_delivery.utility'].get_meta_data(agent_list,model.report_name)
                data['agent_collection_length'] = len(agent_list_ids)
                data['data_length'] = len(agent_list_ids)
                data['offset'] = current_offset
                context = data.get('context') or dict(request.env.context)
                context['get_command'] = True
                response = report.with_context(context).render_qweb_pdf(agent_list_ids,data)
                command_list.append(response.get('command_string'))
                current_offset = len(agent_list_ids)
        else:
            data = request.env['rb_delivery.utility'].get_meta_data(docids,model.report_name)
            docids_ids = [int(i) for i in docids.split(',')]
            data['data_length'] = len(docids_ids)
            data['offset'] = 0
            context = data.get('context') or dict(request.env.context)
            context['get_command'] = True
            response = report.with_context(context).render_qweb_pdf(docids_ids,data)
            command_list.append(response.get('command_string')) 
        payload = {
            'command_string': command_list,
        }

        body = json.dumps(payload)
        print('===========================')
        print(body)
        print('===========================')
        return Response(body, content_type='application/json', status=200)
        

    @http.route('/report_mobile/pdf/', type='json', auth="none")
    def get_report_pdf(self, **rec):
        report = request.jsonrequest.get('report')
        docids = request.jsonrequest.get('records_ids')
        model = request.jsonrequest.get('model')
        context = request.jsonrequest.get('context')
        data = request.jsonrequest.get('data')
        if not context:
            return {'error': _('Context is required'), 'code': 400}

        if not docids:
            return {'error': _('Docids is required'), 'code': 400}

        if not report:
            return {'error': _('Report is required'), 'code': 400}

        if not model:
            return {'error': _('Model is required'), 'code': 400}

        context = json.loads(context)
        if not context:
            return {'error': _('Context is required'), 'code': 400}

        pdf, _ = request.env['ir.actions.report'].with_context(context).search([('report_name','=',report),('model','=',model)]).sudo().render_qweb_pdf(docids, data)
        print(pdf)
        return base64.b64encode(pdf)


class rb_delivery(http.Controller):

    @http.route(['/invoicing/excel_report/<string:model_name>',], type='http', auth="user", csrf=False)
    def get_sale_excel_report(self,model_name, **args):
        if model_name == 'rb_delivery.user':
            file_name = 'user_template'
        elif model_name == 'rb_delivery.order':
            file_name = 'order_template'
        response = request.make_response(
            None,
            headers=[
               ('Content-Type', 'application/vnd.ms-excel'),
               ('Content-Disposition', content_disposition(file_name + '.xls'))
           ]
        )
        output = BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})

        sheet = workbook.add_worksheet("Template")
        data_sheet = workbook.add_worksheet("Details")
        data_sheet.sheet_state = 'hidden'
        areas = request.env['rb_delivery.area'].search([])
        sub_areas = request.env['rb_delivery.sub_area'].search([])
        users = request.env['rb_delivery.user'].search([('role_code','=','rb_delivery.role_business')])
        area_list = []
        sub_area_list = []
        sender_list = []
        header = []
        for area in areas:
            area_list.append(area.name)
        for sub_area in sub_areas:
            sub_area_list.append(sub_area.name)
        for business in users:
            sender_list.append(business.mobile_number)
        data_sheet.write_column('A1', area_list)
        area_source = '=Details!$A$1:$A$'+str(len(area_list))
        data_sheet.write_column('B1', sub_area_list)
        sub_area_source = '=Details!$B$1:$B$'+str(len(sub_area_list))
        data_sheet.write_column('C1', sender_list)
        sender_source = '=Details!$C$1:$C$'+str(len(sender_list))
        if model_name == 'rb_delivery.order':
            user = request.env['res.users'].search([('id','=',request._uid)])
            if user.has_group('rb_delivery.role_business'):
                header = [_("Reference Id"),_("Receiver Name"),_("Receiver Mobile"), _("Receiver Second Mobile Number"), _("Receiver Area"),_("Receiver Sub Area"),_("Receiver Address"),_("Product Note"),_("Note"),_("Total Amount")]
                sheet.data_validation('E2:E1048576', {'validate': 'list','source': area_source})
                sheet.data_validation('F2:F1048576', {'validate': 'list','source': sub_area_source})
            else:
                header = [_("Reference Id"),_("Sender"),_("Receiver Name"),_("Receiver Mobile"), _("Receiver Second Mobile Number"), _("Receiver Area"),_("Receiver Sub Area"),_("Receiver Address"),_("Product Note"),_("Note"),_("Total Amount")]
                sheet.data_validation('F2:F1048576', {'validate': 'list','source': area_source})
                sheet.data_validation('G2:G1048576', {'validate': 'list','source': sub_area_source})
                sheet.data_validation('B2:B1048576', {'validate': 'list','source': sender_source})

        elif model_name == 'rb_delivery.user':
            header = [_("Address"), _("Area"), _("Mobile Number"), _("Username"), _("Password")]
            sheet.data_validation('B2:B1048576', {'validate': 'list','source': area_source})

        index = 0
        for item in header:
            sheet.write(0, index, _(item))
            index += 1



        workbook.close()
        output.seek(0)
        response.stream.write(output.read())
        output.close()
        return response

    # we need to use special login for mobile app to return some needed info like session id
    @http.route('/web/session/authenticate_mobile', type='json', auth="none",csrf=False,website=False)
    def authenticate_mobile(self, db, login, password, base_location=None):
            # Check if user exist in rb_delivery
            response_arr = []
            olivery_user = None
            account_number_registration = request.env['rb_delivery.client_configuration'].sudo().get_param('account_number_registration')
            email_registration = request.env['rb_delivery.utility'].sudo().check_text_characters_or_numbers(login)
            if email_registration:
                if request.env['rb_delivery.client_configuration'].sudo().get_param('email_registration'):
                    olivery_user=request.env['rb_delivery.user'].sudo().search([('email','=',login),'|',('active','=',True),('active','=',False)])
                else:
                    olivery_user=request.env['rb_delivery.user'].sudo().search([('mobile_number','=',login),'|',('active','=',True),('active','=',False)])
            else:
                if account_number_registration:
                    olivery_user=request.env['rb_delivery.user'].sudo().search([('user_sequence','=',login),'|',('active','=',True),('active','=',False)])
                else:
                    olivery_user=request.env['rb_delivery.user'].sudo().search([('mobile_number','=',login),'|',('active','=',True),('active','=',False)])
            if olivery_user==False or len(olivery_user)==0:
                if account_number_registration:
                    response_arr.append({"code": 400, "message":'Account number is not registered in the system.'})
                    return response_arr
                elif email_registration:
                    response_arr.append({"code": 400, "message":'Email is not registered in the system.'})
                    return response_arr
                else:
                    response_arr.append({"code": 400, "message":'Mobile number is not registered in the system.'})

            elif olivery_user:
                # check if it is confirmed or not
                forbidden_roles_ids = request.env['rb_delivery.client_configuration'].sudo().get_param('determine_mobile_platform_login')
                if forbidden_roles_ids and len(forbidden_roles_ids) > 0 and olivery_user.group_id and olivery_user.group_id.id in forbidden_roles_ids :
                    response_arr.append({"code": 401, "message":'You are Prevented from logging in on the web platform, please contact your administrator.'})
                    return response_arr
                elif olivery_user.state!='confirmed' and olivery_user.state!='reconfirmed':
                    response_arr.append({"code": 401, "message":'Your account is not confirmed yet, please contact your administrator.','state':olivery_user.state})
                    return response_arr

            # if it is exist then need to check if it is pending or confirmed
            try:
                request.session.authenticate(db,login,password)
            except:
                response_arr.append({"code": 401, "message":'User not authorized','state':'not_authorized'})
                return response_arr
            else:
               user_info=olivery_user.search_read([('id','=',olivery_user.id)],['id', 'user_id', 'state', 'username', 'mobile_number', 'area_id', 'warehouse', 'email', 'address', 'group_id', 'role_name','role_code','inclusive_delivery','commercial_name','has_customers','player_id','forgot_password','online','is_block_delivery_fee','default_payment_type','default_payment_detail','bank_name','bank_number','wallet_name', 'wallet_number','holder_name'])
               context_info=request.env['ir.http'].session_info()
               response_arr.append({"code": 200, "message":{'context_info':context_info,'user_info':user_info}})
            return response_arr
        
    @http.route('/api/heartbeat', type='http', auth='none', methods=['GET'], save_session=False)
    def heartbeat(self):
        response_data = {'status': 'ok'}
        return request.make_response(
            json.dumps(response_data),
            headers={'Content-Type': 'application/json'}
        )


    @http.route('/rb_delivery/get_form/', auth='public' , methods=['POST'],type="json",save_session=False)
    def get_form(self, **kw):
        form_name = http.request.params['form_name']
        if request._uid:
            inputs = request.env['rb_delivery.mobile_form_input'].get_form(form_name)
        else:
            inputs = request.env['rb_delivery.mobile_form_input'].sudo().get_form(form_name)
        return inputs

    @http.route('/rb_delivery/get_card/', auth='user' , methods=['POST'],type="json")
    def get_card(self, **kw):
        card_name = http.request.params['card_name']
        card = request.env['rb_delivery.mobile_card_item'].get_card(card_name)
        return card

    @http.route('/rb_delivery/area/', auth='public',save_session=False)
    def get_area(self, **kw):
        areas = request.env['rb_delivery.area'].sudo().search_read([['show_in_register','=',True]],['name','code','has_sub_area','country_id'])
        return json.dumps(areas)

    @http.route('/web/readyz/', auth='public',save_session=False)
    def readyz(self, **kw):
        return json.dumps({'result':'true'})

    @http.route('/rb_delivery/payment_type/', auth='public',save_session=False)
    def get_payment_type(self, **kw):
        payment_types = request.env['rb_delivery.payment_type'].sudo().search_read([],['name','code','default'])
        return json.dumps(payment_types)

    @http.route('/rb_delivery/terms_and_conditions',auth='public',save_session=False)
    def get_public_terms_and_conditions(self, **kw):
        terms_and_conditions = ''
        company = request.env['res.company'].sudo().search([])[0]
        terms_and_conditions = company.terms_and_conditions
        return json.dumps(terms_and_conditions)

    @http.route('/get_user_info', auth='public', methods=['GET','POST'],type="json",save_session=False)
    def get_user_info(self,**args):
        user_mobile=args['mobile_number']
        user_info = request.env['rb_delivery.user'].sudo().search_read([('mobile_number','=',user_mobile)],['area_id','address','username','is_company'])
        return user_info[0]

    @http.route('/rb_delivery/general_configuration/', auth='public',save_session=False)
    def get_general_configuration(self, **kw):
        fields = request.env['rb_delivery.general_configuration'].sudo().search_read([],['support_url','support_phone','support_email'])
        return json.dumps(fields)

    @http.route(['/rb_delivery/sub_area/'], type='json', auth="none")
    def get_sub_area(self, **kwargs):
        area_id = http.request.params['area_id']
        sub_areas = request.env['rb_delivery.sub_area'].sudo().search_read([['show_in_register','=',True],'|',['parent_id','=',area_id],['area_parent_id','=',area_id]],['name','code'])
        return sub_areas

    @http.route('/rb_delivery/get_client_config/', auth='public',save_session=False)
    def get_configs(self, **kw):
        config = request.env['rb_delivery.client_configuration'].sudo().search_read(['|',['platform_type','=','web_mobile'],['platform_type','=','mobile']],['key','value','platform_type','text','status','group_ids','related_to_status','related_to_text' ,'related_to_search','related_to_sms' ,'related_to_group_ids'])
        return json.dumps(config)



#     @http.route('/rb_delivery/rb_delivery/objects/', auth='public')
#     def list(self, **kw):
#         return http.request.render('rb_delivery.listing', {
#             'root': '/rb_delivery/rb_delivery',
#             'objects': http.request.env['rb_delivery.rb_delivery'].search([]),
#         })

    #@http.route('/rb_delivery/register/', auth='public')
    # def object(self, obj, **kw):
    #     register_form=obj
    #     return http.request.render('rb_delivery.object', {
    #         'object': obj
    #     })



    # @http.route(['/get_app_version'], type='http',
    #             auth="public", methods=['GET'],
    #             website=True, sitemap=False)
    # def get_app_version(self):
    #     """
    #     Check if company has a logo

    #     :param company_id: ID of the company as an integer
    #     :return: bool
    #     """
    #     version = request.env['ir.module.module'].sudo().search([('name','=','rb_delivery')]).installed_version

    #     return json.dumps({
    #         'version': version,
    #     }, ensure_ascii=False)

    @http.route('/rb_delivery/update_agent', auth='public', methods=['POST'],type="json",save_session=False)
    def update_agent(self,**args):

        #sequence = http.request.params['sequence']
        #agent_id = http.request.params['agent_id']
        agent_id=args['agent_id']
        order_list_sequence=args['order_list_sequence']
        request.env['rb_delivery.order'].sudo().search([('sequence','in',order_list_sequence)]).sudo().write({'assign_to_agent':agent_id})
        return json.dumps({'data':'ok'})


    @http.route('/rb_delivery/register/', auth='public', methods=['POST'],type="json",save_session=False)
    def index(self, **args):
        data = args
        #del request.httprequest.headers['Accept-Language']
        #request.httprequest.headers.to_dict().pop('Accept-Language')
        if 'email' in data and data['email'] == '':
            data['email'] = False
        user=request.env['rb_delivery.user'].sudo().create(data)
        if (request.env['rb_delivery.client_configuration'].sudo().get_param('login_enable_without_confirmation')):
            user.wkf_action_confirm()
        return json.dumps({'data':'ok'})
        #return http.request.render('rb_delivery.object',{'object':data})
		# use "data_in_json" according to your requirement




    @http.route('/get_orders', auth='none', type="json")
    def get_orders(self, **args):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del args['login']
        del args['password']
        del args['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args

        orders_rec = request.env['rb_delivery.order'].search([])
        orders = []
        for rec in orders_rec:
            vals = {
                'id': rec.id,
                'sequence': rec.sequence
            }
            orders.append(vals)
        data = {'status': 200, 'response': orders, 'Length':len(orders_rec), 'message': 'Success' }
        return data

    @http.route('/v2/get_orders', auth='user', type="json")
    def get_orders_v2(self, **args):
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args

        orders_rec = request.env['rb_delivery.order'].search([])
        orders = []
        for rec in orders_rec:
            vals = {
                'id': rec.id,
                'sequence': rec.sequence
            }
            orders.append(vals)
        data = {'status': 200, 'response': orders, 'Length':len(orders_rec), 'message': 'Success' }
        return data

    # for create business user
    @http.route('/create_sender', auth='none', type="json")
    def create_sender(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']

        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])


        if not manager:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args

        if request.jsonrequest:
            if 'mobile_number' in rec and rec['mobile_number']:
                user = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',rec['mobile_number'])])
                if user.id:
                    args = {'code':401,'fail': True, 'message': 'Fail Add there is a user with the same mobile number' }
                    return args
                elif not user.id:
                    vals = rec
                    vals['password'] = rec['user_password']
                    vals['inclusive_delivery'] = True
                    try:
                        user_obj_with_ids=request.env['rb_delivery.utility'].sudo().convert_names_to_ids('rb_delivery.user',rec,request.env)
                        new_user_id = request.env['rb_delivery.user'].create(user_obj_with_ids)
                        args = {'code':200,'success': True, 'message': 'Success Add User', 'id': new_user_id.id }
                    except Exception as e:
                        fail_message = _("User of mobile number ") + rec['mobile_number'] +_(" failed to be added because of ") + str(e)
                        args = {'code':401,'fail': True, 'message': fail_message}
                else:
                    args = {'code':401,'message':'No user was found'}
            else:
                args = {'code':401,'fail': True, 'message': 'Please add mobile_number'}
        return args

    # for create business user
    @http.route('/v2/create_sender', auth='user', type="json")
    def create_sender_v2(self, **rec):

        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])


        if not manager:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args

        if request.jsonrequest:
            if 'mobile_number' in rec and rec['mobile_number']:
                user = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',rec['mobile_number'])])
                if user.id:
                    args = {'code':401,'fail': True, 'message': 'Fail Add there is a user with the same mobile number' }
                    return args
                elif not user.id:
                    vals = rec
                    vals['password'] = rec['user_password']
                    vals['inclusive_delivery'] = True
                    try:
                        new_user_id = request.env['rb_delivery.user'].create(vals)
                        args = {'code':200,'success': True, 'message': 'Success Add User', 'id': new_user_id.id }
                    except Exception as e:
                        fail_message = _("User of mobile number ") + rec['mobile_number'] +_(" failed to be added because of ") + str(e)
                        args = {'code':401,'fail': True, 'message': fail_message}
                else:
                    args = {'code':401,'message':'No user was found'}
            else:
                args = {'code':401,'fail': True, 'message': 'Please add mobile_number'}
        return args

    @http.route('/request_collection', auth='public', methods=['POST'], type='json')
    def request_collection(self, **rec):
        try:
            db = rec.get('db')
            password = rec.get('password')
            login = rec.get('login')
            from_date_str = rec.get('from_date')
            to_date_str = rec.get('to_date')
            statuses = rec.get('status')

            if not all([db, password, login, from_date_str, to_date_str,statuses]):
                return {'code': 400, 'fail': True, 'message': _('Missing required parameters.')}

            allowed_statuses = {'money_received', 'money_in', 'money_out', 'paid', 'completed', 'deleted'}
            if not set(statuses).issubset(allowed_statuses):
                return {'code': 400, 'fail': True, 'message': _('Invalid status provided.')}

            from_date = datetime.strptime(from_date_str, '%Y-%m-%d')
            to_date = datetime.strptime(to_date_str, '%Y-%m-%d')

            uid = request.session.authenticate(db, login, password)
            if not uid:
                return {'code': 403, 'fail': True, 'message': _('Failed to authenticate.')}

            user = request.env['rb_delivery.user'].sudo().search([('user_id', '=', uid)], limit=1)
            if not user:
                return {'code': 404, 'fail': True, 'message': _('User not found.')}

            collections = request.env['rb_delivery.multi_print_orders_money_collector'].sudo().search_read([
                ('business_id', '=', user.id),
                ('create_date', '>=', from_date),
                ('create_date', '<=', to_date),
                ('state', 'in', statuses)
            ])

            total_sum = sum(collection.get('total_cost', 0) for collection in collections)

            return {'code': 200, 'success': True, 'total_sum': total_sum}

        except Exception as e:
            return {'code': 500, 'fail': True, 'message': str(e)}

    @http.route('/create_multi_senders', auth='none', type="json")
    def create_multi_senders(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not manager:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args

        if request.jsonrequest:
            if 'senders_list' in rec and rec['senders_list']:
                senders_recs = rec['senders_list']
                IDs = []
                fail_messages = []
                for rec in senders_recs:
                    if 'mobile_number' in rec and rec['mobile_number']:
                        user = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',rec['mobile_number'])])
                        if user.id:
                            args = {'code':401,'fail': True, 'message': 'Fail Add there is a user with the same mobile number' }
                            return args
                        elif not user.id:
                            vals = rec
                            vals['password'] = rec['user_password']
                            vals['inclusive_delivery'] = True
                            try:
                                new_user = request.env['rb_delivery.user'].create(vals)
                                new_user_id = new_user.id
                            except Exception as e:
                                fail_message = _("User of mobile number ") + rec['mobile_number'] +_(" failed to be added because of ") + str(e)
                                fail_messages.append(fail_message)
                            if new_user_id != '':
                                IDs.append(new_user_id)
                        else:
                            args = {'code':401,'message':'No user was found'}
                    else:
                        args = {'code':401,'fail': True, 'message': 'Please add mobile_number' }

                if len(IDs)>0:
                    args = {'code':200,'success': True, 'message': 'Success Add users', 'IDs': IDs }
                    if len(fail_messages)>0:
                        args['fail_message'] = fail_messages
                else:
                    args = {'code':401,'fail': True, 'message': fail_messages}


        return args

    @http.route('/v2/create_multi_senders', auth='user', type="json")
    def create_multi_senders_v2(self, **rec):

        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not manager:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args

        if request.jsonrequest:
            if 'senders_list' in rec and rec['senders_list']:
                senders_recs = rec['senders_list']
                IDs = []
                fail_messages = []
                for rec in senders_recs:
                    if 'mobile_number' in rec and rec['mobile_number']:
                        user = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',rec['mobile_number'])])
                        if user.id:
                            args = {'code':401,'fail': True, 'message': 'Fail Add there is a user with the same mobile number' }
                            return args
                        elif not user.id:
                            vals = rec
                            vals['password'] = rec['user_password']
                            vals['inclusive_delivery'] = True
                            try:
                                new_user = request.env['rb_delivery.user'].create(vals)
                                new_user_id = new_user.id
                            except Exception as e:
                                new_user_id = ""
                                fail_message = _("User of mobile number ") + rec['mobile_number'] +_(" failed to be added because of ") + str(e)
                                fail_messages.append(fail_message)
                            if new_user_id != '':
                                IDs.append(new_user_id)
                        else:
                            args = {'code':401,'message':'No user was found'}
                    else:
                        args = {'code':401,'fail': True, 'message': 'Please add mobile_number' }

                if len(IDs)>0:
                    args = {'code':200,'success': True, 'message': 'Success Add users', 'IDs': IDs }
                    if len(fail_messages)>0:
                        args['fail_message'] = fail_messages
                else:
                    args = {'code':401,'fail': True, 'message': fail_messages}

        return args

    @http.route('/edit_sender', auth='none', type="json")
    def edit_sender(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not manager:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args
        if request.jsonrequest:
            if 'mobile_number' in rec and rec['mobile_number']:
                mobile_number = rec['mobile_number']
                vals = rec['vals']
                sender_rec = request.env['rb_delivery.user'].search([('mobile_number', '=', mobile_number)])
                if sender_rec.id:
                    try:
                        sender_rec.write(vals)
                        args = {'code':200,'success': True, 'message': 'Success Edit', 'id': sender_rec.id }
                    except Exception as e:
                        fail_message = _("User of mobile number ") + rec['mobile_number'] +_(" failed to be edited because of ") + str(e)
                        args = {'code':401,'fail': True, 'message': fail_message}
                else:
                    args = {'code':401,'message':'No user was found'}
            else:
                args = {'code':401,'message':'Please add mobile number'}
        return args

    @http.route('/v2/edit_sender', auth='user', type="json")
    def edit_sender_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not manager:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args
        if request.jsonrequest:
            if 'mobile_number' in rec and rec['mobile_number']:
                mobile_number = rec['mobile_number']
                vals = rec['vals']
                sender_rec = request.env['rb_delivery.user'].search([('mobile_number', '=', mobile_number)])
                if sender_rec.id:
                    try:
                        sender_rec.write(vals)
                        args = {'code':200,'success': True, 'message': 'Success Edit', 'id': sender_rec.id }
                    except Exception as e:
                        fail_message = _("User of mobile number ") + rec['mobile_number'] +_(" failed to be edited because of ") + str(e)
                        args = {'code':401,'fail': True, 'message': fail_message}
                else:
                    args = {'code':401,'message':'No user was found'}
            else:
                args = {'code':401,'message':'Please add mobile number'}
        return args

    @http.route('/get_areas', auth='none', type="json")
    def get_areas(self, **args):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args

        areas_rec = request.env['rb_delivery.area'].search([])
        areas = []
        for rec in areas_rec:
            vals = {
                'id': rec.id,
                'name': rec.name,
                'code': rec.code
            }
            areas.append(vals)
        data = {'status': 200, 'response': areas, 'message': 'Success' }
        return data

    @http.route('/status', auth='none', type="json",methods=['GET'])
    def status(self, **args):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args

        statuses_rec = request.env['rb_delivery.status'].search(['|',('status_type','=',False),('status_type','=','olivery_order')])
        statuses = []
        for status in statuses_rec:
            vals = {
                'id': status.id,
                'name': status.name,
                'title': status.title
            }
            statuses.append(vals)
        data = {'status': 200, 'response': statuses, 'message': 'Success' }
        return data

    @http.route('/v2/get_areas', auth='user', type="json")
    def get_areas_v2(self, **args):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args

        areas_rec = request.env['rb_delivery.area'].search([])
        areas = []
        for rec in areas_rec:
            vals = {
                'id': rec.id,
                'name': rec.name,
                'code': rec.code
            }
            areas.append(vals)
        data = {'status': 200, 'response': areas, 'message': 'Success' }
        return data

    @http.route(['/get_sub_areas'], type='json', auth="none")
    def get_sub_areas(self, **kwargs):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        # authenticate
        uid = request.session.authenticate(db, login, password)
        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args
        sub_areas = request.env['rb_delivery.sub_area'].sudo().search_read([],['id','name','code','parent_id'])
        sub_area=[]
        for rec in sub_areas:
            area=rec['parent_id'][0]
            areas= request.env['rb_delivery.area'].sudo().search_read([['id','=',area]],['id','name','code'])
            area_info = {}
            sub_area_info = {"id":rec['id'],"name":rec['name'],"code":rec['code']}
            if areas:
                for record in areas:
                    area_info=({"id":record['id'],"name":record['name'],"code":record['code']})
                if area_info:
                    sub_area_info['area'] = area_info
            sub_area.append(sub_area_info)
        return sub_area

    @http.route('/get_payment_types', auth='none', type="json")
    def get_payment_types(self, **args):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args

        areas_rec = request.env['rb_delivery.payment_type'].search([])
        areas = []
        for rec in areas_rec:
            vals = {
                'id': rec.id,
                'name': rec.name,
                'code': rec.code,
                'default': rec.default
            }
            areas.append(vals)
        data = {'status': 200, 'response': areas, 'message': 'Success' }
        return data

    @http.route('/print_waybill', auth='none', type="json")
    def print_waybill(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)
        if 'waybill_type' in rec and rec['waybill_type']:
            if rec['waybill_type'] == 'a4':
                waybill = 'rb_delivery.report_rb_delivery_order_detail_a4_action'
            if rec['waybill_type'] == 'a5':
                waybill = 'rb_delivery.report_rb_delivery_order_detail_action'
            if rec['waybill_type'] == 'delivery_waybill_A6':
                if request.env.ref('olivery_templates.report_rb_delivery_delivery_waybill_A6_action'):
                    waybill = 'olivery_templates.report_rb_delivery_delivery_waybill_A6_action'
                else:
                    return {'code':200,'success': False, 'message': _('olivery_templates.delivery_waybill_A6 not found') }
        else:
            return {'code':200,'success': False, 'message': 'Add waybill_type.' }
        if 'order_ids' in rec and rec['order_ids']:
            orders = request.env['rb_delivery.order'].search([('sequence', 'in', rec['order_ids'])])
            if not orders:
                return {'code': 200, 'success': False, 'message': _('No orders found with the provided IDs.')}
            pdfs = []
            lang = http.request.params.get('lang', 'en_US')
            for order in orders:
                env_context_with_lang = Environment(http.request.cr, http.request.uid, {'lang': lang})
                waybill_record = env_context_with_lang.ref(waybill).sudo()
                pdf, _ = waybill_record.render_qweb_pdf([order.id])
                pdfs.append(pdf)

            combined_pdf = self._combine_pdfs(pdfs)
            data = base64.encodebytes(combined_pdf).decode()

            return {'code': 200, 'success': True, 'file': data}

        if 'order_id' in rec and rec['order_id']:
            order_rec_seq = request.env['rb_delivery.order'].search([('sequence', '=', rec['order_id'])])
            if order_rec_seq:
                pdf, _ = request.env.ref(waybill).sudo().render_qweb_pdf(order_rec_seq.id)
                pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
                data = base64.encodestring(pdf)
                return {'code':200,'success': True, 'File': data }
            else:
                order_rec_ref = request.env['rb_delivery.order'].search([('reference_id', '=', rec['order_id'])])
                if order_rec_ref:
                    pdf, _ = request.env.ref(waybill).sudo().render_qweb_pdf(order_rec_ref.id)
                    pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
                    data = base64.encodestring(pdf)
                    return {'code':200,'success': True, 'File': data }
                else:
                    order_rec = request.env['rb_delivery.order'].search([('id', '=', rec['order_id'])])
                    if order_rec:
                        pdf, _ = request.env.ref(waybill).sudo().render_qweb_pdf(order_rec.id)
                        pdfhttpheaders = [('Content-Type', 'application/pdf'), ('Content-Length', u'%s' % len(pdf))]
                        data = base64.encodestring(pdf)
                        return {'code':200,'success': True, 'File': data }
                    else:
                        return {'code':200,'success': False, 'message': 'There is no order of the same ID.' }

        else:
            return {'code':200,'success': False, 'message': 'Please add order ID.' }

    def _combine_pdfs(self, pdfs):
        output = BytesIO()
        writer = PyPDF2.PdfFileWriter()
        for pdf in pdfs:
            reader = PyPDF2.PdfFileReader(BytesIO(pdf))
            for i in range(reader.getNumPages()):
                writer.addPage(reader.getPage(i))
        writer.write(output)
        return output.getvalue()

    @http.route('/create_order_v2', auth='none', type="json")
    def v2_create_order(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        super_manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        #inclusive_delivery = business.inclusive_delivery

        if not super_manager:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args

        if request.jsonrequest:
            if 'business_information' not in rec:
                args = {'code':401,'fail': True, 'message': 'Please add business information.' }
                return args
            else:
                if 'business_information' in rec:
                    business_information = rec['business_information']
                    if 'mobile_number' in business_information and business_information['mobile_number']:
                        user = request.env['rb_delivery.user'].search([('mobile_number','=',business_information['mobile_number'])])
                        if user and user.id:
                            try:
                               user.write(business_information)
                            except Exception as e:
                                fail_message = _("Failed to be edit user because of ") + str(e)
                                args = {'code':401,'fail': True, 'message': fail_message}
                                return args
                        else:
                            letters = string.ascii_letters
                            password = ''.join(random.choice(letters) for i in range(8))
                            rec['business_information']['password'] = password
                            try:
                               user = request.env['rb_delivery.user'].create(rec['business_information'])
                            except Exception as e:
                                fail_message = _("Failed to be create user because of ") + str(e)
                                args = {'code':401,'fail': True, 'message': fail_message}
                                return args

                del rec['business_information']
                rec['assign_to_business'] = user.id
                inclusive_delivery = user.inclusive_delivery
                if 'reference_id' in rec and rec['reference_id']:
                    order = request.env['rb_delivery.order'].sudo().search([('reference_id','=',rec['reference_id'])])
                    if order and order.id:
                        args = {'code':401,'fail': True, 'message': 'Order with the same Reference ID already exists'}
                        return args
                if not 'cost' in rec:
                    args = {'code':401,'fail': True, 'message': 'Add Failed please add cost value'}
                else:
                    vals = rec
                    if inclusive_delivery:
                        vals['copy_total_cost'] = rec['cost']
                    try:
                        new_order_id = request.env['rb_delivery.order'].create(vals)
                        args = {'code':200,'success': True, 'message': 'Success Add', 'id': new_order_id.id ,'Sequence':new_order_id.sequence,'Reference':new_order_id.reference_id,'Delivery cost':new_order_id.delivery_cost }
                    except Exception as e:
                        fail_message = _("Order failed to be added because of ") + str(e)
                        args = {'code':401,'fail': True, 'message': fail_message}
        return args

    # for applications that using async mode like JS and Nodejs or webhookds
    @http.route('/async_create_order', auth='none', type="json")
    def async_create_order(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password'].encode("ascii", "ignore")
        login= http.request.params['login'].encode("ascii", "ignore")
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        inclusive_delivery = business.inclusive_delivery

        if not business:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args

        if business.role_code != 'rb_delivery.role_business':
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not business user.')}
            return args
        if request.jsonrequest:
            if 'reference_id' in rec and rec['reference_id']:
                order = request.env['rb_delivery.order'].sudo().search([('reference_id','=',rec['reference_id']),('state','!=','deleted')])
                if order and order.id:
                    args = {'code':401,'Fail': True, 'Message': 'Order with the same Reference ID already exists'}
                    return args
            if not 'cost' in rec:
                args = {'code':401,'Fail': True, 'Message': 'Add Failed please add cost value'}
            else:
                vals = rec
                if inclusive_delivery:
                    vals['copy_total_cost'] = rec['cost']
                try:
                    request.env['rb_delivery.order'].with_delay(channel="root.api",max_retries=2).create(vals)
                    args = {'code':200,'Success': True, 'Message': 'Success Add New order to the Queue'}
                except Exception as e:
                    fail_message = _("Order failed to be added because of ") + str(e)
                    args = {'code':401,'Fail': True, 'Message': fail_message}

        return args

    @http.route('/get-company-support-number', methods=['GET','POST'] ,auth='public',type="json")
    def get_company_support_number(self, **rec):
        company = http.request.env['res.company'].sudo().search_read([],['phone'])[0]
        return company['phone']


    @http.route('/fuzzy_create_order', auth='none', type="json")
    def new_create_order(self, **rec):
            db = http.request.params['db']
            password = http.request.params['password']
            login = http.request.params['login']
            del rec['login']
            del rec['password']
            del rec['db']
            # authenticate
            uid = request.session.authenticate(db, login, password)

            if uid == False:
                args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
                return args

            business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

            if not business:
                args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
                return args

            if business.role_code != 'rb_delivery.role_business':
                args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not business user.')}
                return args
            inclusive_delivery = business.inclusive_delivery

            if request.jsonrequest:
                if 'order_date' in rec and rec['order_date']:
                    del rec['order_date']
                if rec.get('assign_to_business'):
                    sender = request.env['rb_delivery.user'].search([('mobile_number','=',rec.get('assign_to_business')),('role_code','=','rb_delivery.role_business')])
                    if sender:
                        rec['assign_to_business'] = sender.id
                        inclusive_delivery = sender.inclusive_delivery
                    else:
                        fail_message = _("Sender of mobile number %s does not exist in the system or you don't have access to the user.")%(rec['assign_to_business'])
                        args = {'code':401,'fail': True, 'message': fail_message}
                        return args
                if 'reference_id' in rec and rec['reference_id']:
                    order = request.env['rb_delivery.order'].search([('reference_id','=',rec['reference_id']),('state','!=','deleted')],limit=1)
                    if order and order.id:
                        args = {'code':401,'fail': True,'order_exists':True,'order_sequence':order.sequence,'order_reference':order.reference_id, 'message': _('Order with the same Reference ID already exists')}
                        return args
                if not 'cost' in rec:
                    args = {'code':401,'fail': True, 'message': 'Add Failed please add cost value'}
                else:
                    vals = rec
                    if inclusive_delivery:
                        vals['copy_total_cost'] = rec['cost']

                    try:
                        if vals.get('customer_area') and isinstance(vals.get('customer_area'), str):
                            customer_area_val = vals.get('customer_area')
                            area_list = request.env['rb_delivery.area'].sudo().search([('show_in_create', '=', True)])
                            area_id = request.env['rb_delivery.order'].fuzzy_search(customer_area_val, area_list)
                            if area_id:
                                vals['customer_area'] = area_id.id
                        if vals.get('customer_sub_area') and isinstance(vals.get('customer_sub_area'), str):
                            customer_sub_area_val = vals.get('customer_sub_area')
                            sub_area_list = request.env['rb_delivery.sub_area'].sudo().search([('show_in_create', '=', True)])
                            sub_area_id = request.env['rb_delivery.order'].fuzzy_search(customer_sub_area_val, sub_area_list)
                            if sub_area_id:
                                vals['customer_sub_area'] = sub_area_id.id
                        if vals.get('address_tag') and isinstance(vals.get('address_tag'), str):
                            address_tag_val = vals.get('address_tag')
                            address_tag_list = request.env['rb_delivery.address_tags'].sudo().search([])
                            address_tag_id = request.env['rb_delivery.order'].fuzzy_search(address_tag_val, address_tag_list)
                            if address_tag_id:
                                vals['address_tag'] = address_tag_id.id
                        company = http.request.env['res.company'].sudo().search_read([],['base_url'])[0]
                        new_order_id = request.env['rb_delivery.order'].create(vals)
                        new_order_id.message_post(body=_("Order has been created through create_order API"))
                        args = {'code':200,'success': True, 'message': 'Success Add', 'id': new_order_id.id ,'sequence':new_order_id.sequence,'reference':new_order_id.reference_id,'delivery_cost':new_order_id.delivery_cost }
                        return_waybill_a4 = request.env['rb_delivery.client_configuration'].sudo().get_param('return_waybill_in_api')
                        if company.get("base_url") and return_waybill_a4:
                            args['tracking_url'] = company.get("base_url")+'/order_tracking/'+new_order_id.sequence

                            # Create an attachment record
                            attachment = request.env['ir.attachment'].sudo().create({
                                'name': 'waybill_a4.pdf',
                                'datas_fname': 'waybill_a4.pdf',
                                'res_model': 'rb_delivery.order',
                                'public':True
                            })
                            request.env['rb_delivery.order'].with_delay(channel="root.report",max_retries=2).create_pdf_report([new_order_id.id],attachment,'rb_delivery.report_rb_delivery_order_detail_a4_action')

                            pdf_link = '/web/image/%s' % attachment.id
                            args['waybill_url'] = company.get("base_url")+pdf_link

                    except Exception as e:
                        fail_message = _("Order failed to be added because of ") + str(e)
                        args = {'code':401,'fail': True, 'message': fail_message}

            return args

    @http.route('/create_order', auth='none', type="json")
    def create_order(self, **rec):
            db = http.request.params['db']
            password = http.request.params['password']
            login = http.request.params['login']
            del rec['login']
            del rec['password']
            del rec['db']
            # authenticate
            uid = request.session.authenticate(db, login, password)

            if uid == False:
                args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
                return args

            business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

            inclusive_delivery = business.inclusive_delivery

            if not business:
                args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
                return args

            if business.role_code != 'rb_delivery.role_business':
                args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not business user.')}
                return args

            if request.jsonrequest:
                if 'order_date' in rec and rec['order_date']:
                    del rec['order_date']
                if rec.get('reschedule_date'):
                    rec['reschedule_date'] = request.env['rb_delivery.order'].check_reschedule_date_from_api(rec.get('reschedule_date'))
                if rec.get('assign_to_business'):
                    sender = request.env['rb_delivery.user'].search([('mobile_number','=',rec.get('assign_to_business')),('role_code','=','rb_delivery.role_business')])
                    if sender:
                        rec['assign_to_business'] = sender.id
                    else:
                        fail_message = _("Sender of mobile number %s does not exist in the system or you don't have access to the user.")%(rec['assign_to_business'])
                        args = {'code':401,'fail': True, 'message': fail_message}
                        return args
                if 'reference_id' in rec and rec['reference_id']:
                    order = request.env['rb_delivery.order'].search([('reference_id','=',rec['reference_id']),('state','!=','deleted')],limit=1)
                    if order and order.id:
                        args = {'code':401,'fail': True,'order_exists':True,'order_sequence':order.sequence,'order_reference':order.reference_id, 'message': _('Order with the same Reference ID already exists')}
                        return args
                if not 'cost' in rec:
                    args = {'code':401,'fail': True, 'message': 'Add Failed please add cost value'}
                else:
                    vals = rec
                    if inclusive_delivery:
                        vals['copy_total_cost'] = rec['cost']

                    try:
                        company = http.request.env['res.company'].sudo().search_read([],['base_url'])[0]
                        new_order_id = request.env['rb_delivery.order'].with_context(from_api=True).create(vals)
                        new_order_id.message_post(body=_("Order has been created through create_order API"))
                        args = {'code':200,'success': True, 'message': 'Success Add', 'id': new_order_id.id ,'Sequence':new_order_id.sequence,'Reference':new_order_id.reference_id,'Delivery cost':new_order_id.delivery_cost }
                        confs = request.env['rb_delivery.client_configuration'].sudo().get_param(['return_waybill_in_api', 'waybill_in_api'])
                        waybill = request.env['ir.actions.report'].sudo().browse([confs['waybill_in_api']]).report_name if confs['waybill_in_api'] else 'rb_delivery.report_rb_delivery_order_detail_a4_action'
                        if company.get("base_url") and confs['return_waybill_in_api'] and waybill:
                            args['tracking_url'] = company.get("base_url")+'/order_tracking/'+new_order_id.sequence

                            # Create an attachment record
                            attachment = request.env['ir.attachment'].sudo().create({
                                'name': 'waybill.pdf',
                                'datas_fname': 'waybill.pdf',
                                'res_model': 'rb_delivery.order',
                                'public':True
                            })
                            
                            request.env['rb_delivery.order'].with_delay(channel="root.report",max_retries=2).create_pdf_report([new_order_id.id],attachment,waybill)

                            pdf_link = '/web/image/%s' % attachment.id
                            args['waybill_url'] = company.get("base_url")+pdf_link

                    except Exception as e:
                        fail_message = _("Order failed to be added because of ") + str(e)
                        args = {'code':401,'fail': True, 'message': fail_message}
                        request.env.cr.rollback()

            return args


    @http.route('/v2/create_order', auth='user', type="json")
    def create_order_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        inclusive_delivery = business.inclusive_delivery

        if not business:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args

        if business.role_code != 'rb_delivery.role_business':
            args = {'code':401,'fail': True, 'message': _('User is not business user.')}
            return args

        if request.jsonrequest:
            if 'reference_id' in rec and rec['reference_id']:
                order = request.env['rb_delivery.order'].sudo().search([('reference_id','=',rec['reference_id'])])
                if order and order.id:
                    args = {'code':401,'fail': True, 'message': 'Order with the same Reference ID already exists'}
                    return args
            if not 'cost' in rec:
                args = {'code':401,'fail': True, 'message': 'Add Failed please add cost value'}
            else:
                vals = rec
                if inclusive_delivery:
                    vals['copy_total_cost'] = rec['cost']
                try:
                    new_order_id = request.env['rb_delivery.order'].create(vals)
                    args = {'code':200,'success': True, 'message': 'Success Add', 'id': new_order_id.id ,'Sequence':new_order_id.sequence,'Reference':new_order_id.reference_id,'Delivery cost':new_order_id.delivery_cost }
                except Exception as e:
                    fail_message = _("Order failed to be added because of ") + str(e)
                    args = {'code':401,'fail': True, 'message': fail_message}

        return args


    @http.route('/create_order_super', auth='none', type="json")
    def create_order_super(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        super_manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not super_manager:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args
        if 'sender_id' not in rec:
            return {'status':403,'message':'Please add sender_id'}
        user = request.env['rb_delivery.user'].sudo().search([('id','=',rec['sender_id'])])
        if user.id:
            inclusive_delivery = user.inclusive_delivery
            user_id = user.id
        else:
            user_mob = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',rec['sender_id'])])
            if user_mob.id:
                inclusive_delivery = user_mob.inclusive_delivery
                user_id = user_mob.id
            else:
                return {'status':403,'message':'ID/Mobile number entered is wrong'}
        if request.jsonrequest:
            # for inclusive delivery
            if not 'cost' in rec:
                args = {'code':401,'success': False, 'message': 'Add Failed please add cost value' }
            else:
                vals = rec
                if inclusive_delivery:
                    vals['copy_total_cost'] = rec['cost']
                vals['assign_to_business'] = user_id
                try:
                    new_order_id = request.env['rb_delivery.order'].create(vals)
                    args = {'code':200,'success': True, 'message': 'Success Add', 'id': new_order_id.id ,'Sequence':new_order_id.sequence,'Reference':new_order_id.reference_id,'Delivery cost':new_order_id.delivery_cost  }
                except Exception as e:
                    fail_message = _("Order failed to be added because of ") + str(e)
                    args = {'code':401,'fail': True, 'message': fail_message}
        return args

    @http.route('/v2/create_order_super', auth='user', type="json")
    def create_order_super_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        super_manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not super_manager:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args
        if 'sender_id' not in rec:
            return {'status':403,'message':'Please add sender_id'}
        user = request.env['rb_delivery.user'].sudo().search([('id','=',rec['sender_id'])])
        if user.id:
            inclusive_delivery = user.inclusive_delivery
            user_id = user.id
        else:
            user_mob = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',rec['sender_id'])])
            if user_mob.id:
                inclusive_delivery = user_mob.inclusive_delivery
                user_id = user_mob.id
            else:
                return {'status':403,'message':'ID/Mobile number entered is wrong'}
        if request.jsonrequest:
            # for inclusive delivery
            if not 'cost' in rec:
                args = {'code':401,'success': False, 'message': 'Add Failed please add cost value' }
            else:
                vals = rec
                if inclusive_delivery:
                    vals['copy_total_cost'] = rec['cost']
                vals['assign_to_business'] = user_id
                try:
                    new_order_id = request.env['rb_delivery.order'].create(vals)
                    args = {'code':200,'success': True, 'message': 'Success Add', 'id': new_order_id.id ,'Sequence':new_order_id.sequence,'Reference':new_order_id.reference_id,'Delivery cost':new_order_id.delivery_cost }
                except Exception as e:
                    fail_message = _("Order failed to be added because of ") + str(e)
                    args = {'code':401,'fail': True, 'message': fail_message}
        return args


    @http.route('/create_multi_orders', auth='none', type="json")
    def create_multi_orders(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if rec.get('lang'):
            request.env.user.lang = request.env['rb_delivery.utility'].find_closest_language(rec.get('lang'))

        if not business:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args

        if business.role_code != 'rb_delivery.role_business':
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not business user.')}
            return args
        inclusive_delivery = business.inclusive_delivery
        if request.jsonrequest:
            if rec['orders_list']:
                orders_recs = rec['orders_list']
                IDs = []
                sequences = []
                references = []
                fail_messages = []
                failed_refs = []
                existing_records = []
                existing_sequence_records = []
                default_area = request.env.ref('rb_delivery.area_vhub_default').id
                for rec in orders_recs:
                    with request.env.cr.savepoint():
                        if 'customer_area' in rec and rec['customer_area']:
                            area_name = rec['customer_area']
                            area_based_map = False
                            area_maps = business.area_map
                            for area_map in area_maps:
                                if area_map.name == area_name and default_area != area_map.id:
                                    area_based_map = area_map.area_id
                            if area_based_map:
                                rec['customer_area'] = area_based_map.id
                        if not 'cost' in rec:
                            args = {'code':401,'fail': True, 'message': _('Failed to Add orders please add value of cost')}
                        else:
                            vals = rec
                            if 'reference_id' in vals and vals['reference_id']:
                                order = request.env['rb_delivery.order'].sudo().search([('assign_to_business','=',business.id),('reference_id','=',vals['reference_id']),('state','not in',['canceled','deleted'])])
                                if order:
                                    existing_records.append(vals['reference_id'])
                                    existing_sequence_records.append(order.sequence)
                                    continue
                            if 'assign_to_business' in vals and vals['assign_to_business']:
                                business_user = request.env['rb_delivery.user'].search([('mobile_number','=',vals['assign_to_business'])])
                                if not business_user:
                                    fail_message = ''
                                    if 'reference_id' in vals and vals['reference_id']:
                                        fail_message = _('Order of sequence ')+str(vals['reference_id']) + _(' failed to be added because business mobile does not exist')
                                        failed_refs.append(vals['reference_id'])
                                    else:
                                        fail_message = ('Order of sequence failed to be added because business mobile does not exist')
                                    fail_messages.append(fail_message)
                                    continue
                                else:
                                    inclusive_delivery = business_user.inclusive_delivery
                                    vals['assign_to_business'] = business_user.id
                            if inclusive_delivery:
                                vals['copy_total_cost'] = rec['cost']
                            
                            try:
                                new_order = request.env['rb_delivery.order'].with_context(lang=request.env.user.lang).create(vals)
                                new_order.message_post(body=_("Order has been created through create_multi_orders API"))
                                new_order_id = new_order.id
                                new_order_seq = new_order.sequence
                                new_order_ref = new_order.reference_id
                            except Exception as e:
                                new_order_id = ""
                                new_order_seq = ""
                                fail_message = ''
                                if 'reference_id' in vals and vals['reference_id']:
                                    fail_message = _('Order of sequence ')+str(vals['reference_id']) + _(' failed to be added because of ') + str(e)
                                    failed_refs.append(vals['reference_id'])
                                else:
                                    fail_message = str(e)
                                fail_messages.append(fail_message)
                            if new_order_id != "" and new_order_seq != "":
                                IDs.append(new_order_id)
                                sequences.append(new_order_seq)
                                references.append(new_order_ref)


                    if len(IDs)>0 and len(sequences)>0:
                        args = {'code':200,'success': True, 'message': _('Success Add orders'), 'IDs': IDs, 'Sequences':sequences, 'References':references }
                        if len(fail_messages)>0:
                            args['fail_message'] = fail_messages
                        if len(failed_refs)>0:
                            args['fail_references'] = failed_refs
                        if len(existing_records)>0:
                            args['existing_records'] = existing_records
                        if len(existing_sequence_records)>0:
                            args['existing_sequence_records'] = existing_sequence_records
                    else:
                        args = {'code':401,'fail': True, 'message': fail_messages}
                        if len(failed_refs)>0:
                            args['fail_references'] = failed_refs
                        if len(existing_records)>0:
                            args['existing_records'] = existing_records
                        if len(existing_sequence_records)>0:
                            args['existing_sequence_records'] = existing_sequence_records
        return args


    @http.route('/create_super_multi_orders', auth='user', type="json")
    def create_super_multi_orders(self, **rec):

        context = request.jsonrequest.get('context')
        orders_list = request.jsonrequest.get('orders_list', [])

        if not context:
            return {'code': 403, 'fail': True, 'message': _('Failed to authenticate.')}

        context = json.loads(context)

        if orders_list:

            orders_list = request.env['rb_delivery.utility'].sudo().convert_lines_to_orders('rb_delivery.order', orders_list)
            new_orders = request.env['rb_delivery.order'].with_context(from_excel=True).create_multi(orders_list)

            for order in new_orders:
                order.with_context(original_uid=request.env.user.partner_id.id).sudo().message_post(body=_("Order created by %s using the new excel") % (request.env.user.name))
            return request.env['rb_delivery.utility'].sudo().get_excel_result(new_orders)
        else:
            return {'code': 401, 'fail': True, 'message': _('Please provide a list of orders to create.')}


    @http.route('/create_order_super_multi', auth='none', type="json")
    def create_order_super_multi(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        super_manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not super_manager:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args

        order_list=rec['order_list']
        original_rec=rec
        order_list_final=[]
        for rec in order_list:
            rec=request.env['rb_delivery.utility'].sudo().convert_names_to_ids('rb_delivery.order',rec,request.env)
            if 'assign_to_business' not in rec:
                return {'status':403,'message':'Please add sender_id'}
            user = request.env['rb_delivery.user'].sudo().search([('id','=',rec['assign_to_business'])])
            if user.id:
                inclusive_delivery = user.inclusive_delivery
                user_id = user.id
            else:
                user_mob = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',rec['assign_to_business'])])
                if user_mob.id:
                    inclusive_delivery = user_mob.inclusive_delivery
                    user_id = user_mob.id
                else:
                    return {'status':403,'message':'ID/Mobile number entered is wrong'}
            if request.jsonrequest:
                # for inclusive delivery
                if not 'cost' in rec:
                    args = {'code':401,'success': False, 'message': 'Add Failed please add cost value' }
                else:
                    vals = rec
                    if inclusive_delivery:
                        vals['copy_total_cost'] = rec['cost']
                    vals['assign_to_business'] = user_id
                    order_list_final.append(vals)


        try:
           results_multi=request.env['rb_delivery.order'].create_multi(order_list_final)
           args = {'code':200,'success': True, 'message': 'Success Add', 'id': results_multi}
        except Exception as e:
           fail_message = _("Order failed to be added because of ") + str(e)
           args = {'code':401,'fail': True, 'message': fail_message}
        return args



    @http.route('/validate_orders_super_multi', auth='user', type="json")
    def validate_orders_super_multi(self, **rec):

        context = request.jsonrequest.get('context')
        orders_list = request.jsonrequest.get('orders_list', [])
        field_list = request.jsonrequest.get('field_list', [])

        if not context:
            return {'code': 401, 'fail': True, 'message': _('Please provide a context.')}


        context = json.loads(context)

        if context:
            if not field_list:
                    return {'code': 401, 'fail': True, 'message': _('Please provide a list of fields to validate.')}
            if not orders_list:
                    return {'code': 401, 'fail': True, 'message': _('Please provide a list of orders to validate.')}
            errors = []
            index = 0
            import copy
            mapped_reference_business = []
            data_list = []
            float_fields,date_fields = request.env['rb_delivery.order'].get_float_date_fields()
            args = {'date_fields':[]}
            order_lines = []
            importing_user = request.env['rb_delivery.user'].sudo().search([('user_id','=',request._uid)],limit=1)
            
            for order in orders_list:
                string_array = [str(x) if x is not None else '' for x in order]
                edit_fields = copy.copy(field_list)
                for field in edit_fields:
                    if field in date_fields and string_array[edit_fields.index(field)] and str(string_array[edit_fields.index(field)]).isnumeric() and int(string_array[edit_fields.index(field)]) > 30000:
                        date_val = datetime(1899, 12, 30) + timedelta(days=int(string_array[edit_fields.index(field)]))
                        date_val = datetime.strftime(date_val,"%Y-%m-%d %H:%M:%S")
                        args['date_fields'].append({'col_index':edit_fields.index(field),'value':date_val,'row_index':orders_list.index(order)})
                        string_array[edit_fields.index(field)] = date_val
                        
                string_array = [string_array]
                
                import_fields,data,messages,line_record = request.env['rb_delivery.order'].guard_import(edit_fields,string_array)
                data_list.append(data[0])
                check_val = importing_user.mobile_number if importing_user.role_code == 'rb_delivery.role_business' and 'assign_to_business' not in field_list else order[import_fields.index('assign_to_business')] if 'assign_to_business' in field_list else False
                if 'reference_id' in field_list and check_val and not line_record:
                    if order[import_fields.index('reference_id')] and check_val:
                        business = False
                        if importing_user.role_code == 'rb_delivery.role_business' and 'assign_to_business' not in field_list:
                            business = importing_user
                        else:
                            business = request.env['rb_delivery.user'].search(['|','|',('mobile_number','=',check_val),('username','=',check_val),('user_sequence','=',check_val)])
                        family_ids = []
                        match_found = False

                        if business.parent_id:
                            family_ids = [business.id]
                            parent_childs = list(filter(lambda user_id: user_id != business.id , business.parent_id.child_ids.ids))
                            family_ids += parent_childs

                        elif business.child_ids:
                            family_ids = business.child_ids.ids
                            family_ids.append(business.id)

                        else:
                            family_ids = [business.id]

                        for mapped in mapped_reference_business:
                            if mapped['reference_id'] == order[import_fields.index('reference_id')] and mapped['business'] in family_ids:
                                match_found = True
                                break
                        if not match_found:
                            mapped_reference_business.append({'reference_id':order[import_fields.index('reference_id')],'business':business.id})
                        else:
                            messages.append(_("Reference ID %s already exists in the excel import for the same business or his sub users, you can import the same reference for the same business or any of his sub users only once in the import transaction")%(order[import_fields.index('reference_id')]))

                if messages:
                    for message in messages:
                        message = re.sub(r'\[.*?\]\{\[\[\(\d+\) .*?\]\]\}', '', message)

                    errors.append({'index': index, 'messages': messages})
                else:
                    errors.append(False)
                order_lines.append(line_record)
                index += 1
                if line_record and len(order_lines) > 1:
                    closest = max(i for i, v in enumerate(order_lines) if not v)
                    if closest > -1:
                        if not errors[closest]:
                            errors[closest] = {'index': closest, 'messages': messages}
                        else:
                            errors[closest]['messages'].extend(messages)
            errors = [error for error in errors if error and error.get('messages')]
            args['code'], args['success'] = (401, False) if errors else (200, True)
            if errors:
                args.update({
                    'message': errors
                })
            return args



    @http.route('/get_field_options', auth='user', type="json")
    def get_field_options(self, **rec):

        context = request.jsonrequest.get('context')
        field = request.jsonrequest.get('field')
        domain = request.jsonrequest.get('domain', [])

        model = request.jsonrequest.get('model')

        if not model or model == '':
            model = 'rb_delivery.order'


        if not field:
            return {'code': 401, 'fail': True, 'message': _('Please provide a field name.')}

        if not domain:
            domain = []
        else:
            domain = json.loads(domain.replace('\'', '"'))

        field_name = field
        original_model_name = model
        model = request.env[model]
        field = model._fields.get(field_name)

        if field:
            if field.type == 'many2one':
                if original_model_name == 'rb_delivery.order':
                    model_name = field.comodel_name
                    options = model.get_field_options(field_name, domain, model_name)
                    return {'code': 200, 'success': True, 'isManyToOne': True, 'options': options}
                else:
                    model_name = field.comodel_name
                    options = request.env[model_name].search_read(domain, ['id', 'display_name'])
                    options = [{'name': [record['display_name']], 'id': record['id']} for record in options]
                    return {'code': 200, 'success': True, 'isManyToOne': True, 'options': options}




            elif field.type == 'selection':
                if isinstance(field.selection, str):
                    other_model = request.env[model]
                    selection_function = getattr(other_model, field.selection)
                    options = selection_function()
                else:
                    # field.selection is a static list
                    options = field.selection

                options_list = [{'name': value, 'id': key} for key, value in options]
                return {'code': 200, 'success': True, 'isManyToOne': True, 'options': options_list}

        return {'code': 200, 'success': True, 'isManyToOne': False, 'isSelection': False, 'options': []}


    @http.route('/call_function', auth='user', type="json")
    def call_function(self, **rec):
        result = request.jsonrequest
        request_body = result['params']
        model = request_body['model']
        if 'method' in request_body:
            method = request_body['method']
        else:
            return {
                "result": {
                    "code": 400,
                    "message": 'MUST_SPECIFY_METHOD',
                    "result":False,
                    "success": False
                }
            }

        args = request_body['args'] if 'args' in request_body else {}
        kwargs = request_body['kwargs'] if 'kwargs' in request_body else {}
        try:
            record = call_kw(request.env[model], method, args, kwargs)
            return {
                "result": {
                    "code": 200,
                    "message": 'SUCCESS_CALLING_METHOD',
                    "result":record,
                    "success": True
                }
            }
        except Exception as e:
            return {
                "result": {
                    "code": 400,
                    "message": e,
                    "result":False,
                    "success": False
                }
            }



    @http.route('/get_records', auth='user', type="json")
    def get_records(self, **kw):
        result = request.jsonrequest
        if result['params']:
            request_body = result['params']
            model = request_body['model']
            context = dict(request._context)
            context['lang'] = request.env.user.lang
            request._context = frozendict(context)
            domain = request_body['domain'] if 'domain' in request_body else []
            fields = request_body['fields'] if 'fields' in request_body else []
            offset = request_body['offset'] if 'offset' in request_body else 0
            limit = request_body['limit'] if 'limit' in request_body else 0
            sort = request_body['sort'] if 'sort' in request_body else False
            if domain and request.env[model]:
                model = request.env[model]
                fields = model.fields_get(allfields=['active'])
                if 'active' in fields:
                    domain.append(('active', '=', True))
            try:
                record = request.env[model].with_context(lang=request.env.user.lang).search_read(domain,fields,offset,limit,sort)
                return {
                    "result": {
                        "code": 200,
                        "message": 'SUCCESS_GETTING_RECORD',
                        "result":record,
                        "success": True
                    }
                }

            except Exception as e:
                return {
                    "result": {
                        "code": 400,
                        "message": e,
                        "result":False,
                        "success": False
                    }
                }
        else:
            return result['error']

    @http.route('/get_order_fields', auth='user', type="json")
    def get_order_fields(self, **rec):
        exclude_fields = {'follow_up_orders_map'}

        fields = request.env['rb_delivery.order'].sudo()._fields
        result = []

        input_fields = request.jsonrequest.get('fields', [])
        lang = request.env.user.lang or 'en_US'

        original_field_names = {}

        # Load translations once
        result_translation = request.env['ir.translation'].search_read(
            ['|', ('value', 'in', input_fields), ('source', 'in', input_fields)],
            ['source', 'value']
        )

        for input_field in input_fields:
            for field_name, field_obj in fields.items():
                if input_field == field_obj.string or input_field == _(field_obj.string):
                    original_field_names[field_name] = input_field
                    break
                if result_translation:
                    for translation in result_translation:
                        if translation['source'] == field_obj.string and (input_field == translation['source'] or input_field == translation['value']):
                            original_field_names[field_name] = translation['value']
                            break

        # Function to handle nested one2many fields
        def get_one2many_subfields(field_name, field_obj, prefix_label):
            subfields = []
            submodel_fields = request.env[field_obj.comodel_name].sudo()._fields
            for sub_name, sub_obj in submodel_fields.items():
                if sub_obj.readonly or sub_obj.compute:
                    continue
                subfields.append({
                    'id': f"{field_name}/{sub_name}",
                    'name': f"{prefix_label} / {_(sub_obj.string)}",
                    'type': sub_obj.type
                })
            return subfields

        for field_name, field_obj in fields.items():
            if field_name in exclude_fields or field_obj.readonly or field_obj.compute:
                continue

            translated_field_name = _(field_obj.string)
            field_info = {
                'id': field_name,
                'name': translated_field_name,
                'type': field_obj.type
            }

            if field_name in original_field_names:
                field_info['name'] = original_field_names[field_name]

            result.append(field_info)
            if field_obj.type == 'one2many':
                subfields = get_one2many_subfields(field_name, field_obj, _(field_info['name']))
                result.extend(subfields)

        return {'code': 200, 'success': True, 'fields': result}


    @http.route('/fuzzy_search/order_field', auth='public', type="json")
    def fuzzy_search_order_field(self, **rec):
        result = request.jsonrequest
        if result['params']:
            request_body = result['params']
            return_res = []
            conf = request.env['rb_delivery.client_configuration'].sudo().get_param(['fuzzy_search_accepted_ratio', 'fuzzy_search_field_highest_ratio'])
            short_terms = request.env['rb_delivery.client_configuration'].sudo().search_read([('key','=','order_fields_terms_json')],['key','value','platform_type','text','status','group_ids','attachment'])
            short_terms_json = base64.b64decode(short_terms[0]['attachment']).decode('utf-8')
            json_data = json.loads(short_terms_json)
            accepted_ratio = 0.5
            if conf['fuzzy_search_accepted_ratio']:
                accepted_ratio = float(conf['fuzzy_search_accepted_ratio'])
            highest_ration_fixed = 0.8
            if conf['fuzzy_search_field_highest_ratio'] and float(conf['fuzzy_search_field_highest_ratio']):
                highest_ration_fixed = float(conf['fuzzy_search_field_highest_ratio'])
            fields = request.env['rb_delivery.order'].sudo()._fields
            fields = {
                field_name: field
                for field_name, field in fields.items()
                if not field.readonly and not field.compute and not field.related
            }
            for expected_name in request_body['expected_names']:
                expected_name = expected_name.lower()
                best_match = None
                highest_ratio = 0.0
                id = None
                type = None
                another_names = None
                if json_data:
                    done_searching = False
                    for term_obj in json_data:
                        if done_searching:
                            break
                        if term_obj.get('terms') and term_obj.get('field'):
                            for term in term_obj.get('terms'):
                                ratio = request.env['rb_delivery.utility'].sudo().fuzzy_search_matcher(expected_name, term.lower())
                                if ratio > highest_ratio and ratio >= accepted_ratio:
                                    highest_ratio = ratio
                                if highest_ratio > highest_ration_fixed:
                                    field = request.env['ir.model.fields'].search_read([('name', '=', term_obj.get('field'))])[0]
                                    best_match = term
                                    id = term_obj.get('field')
                                    type = field.get('ttype')
                                    another_names = field.get('field_description')
                                    done_searching = True
                                    break

                if highest_ratio < highest_ratio+0.05:

                    for field_name, field_obj in fields.items():
                        if highest_ratio > 0.8:
                            break
                        result_translation = request.env['ir.translation'].sudo().search_read(['|',('value', '=', field_obj.string),('source', '=', field_obj.string)], ['source', 'value'], limit=10)
                        ratio = request.env['rb_delivery.utility'].sudo().fuzzy_search_matcher(expected_name, field_obj.string)    
                        if ratio > highest_ratio and ratio >= accepted_ratio:
                            highest_ratio = ratio
                            best_match = field_obj.string
                            id = field_name
                            type = field_obj.type
                        for tanslation in result_translation:
                            if highest_ratio > 0.8:
                                break
                            compare_with = tanslation['value']
                            if compare_with == field_obj.string:
                                compare_with = tanslation['source']
                            ratio = request.env['rb_delivery.utility'].sudo().fuzzy_search_matcher(expected_name, compare_with)
                            if ratio > highest_ratio and ratio >= accepted_ratio:
                                highest_ratio = ratio
                                best_match = compare_with
                                id = field_name
                                type = field_obj.type
                                another_names = field_obj.string
                return_res.append({'id': id, 'name': best_match, 'type': type, 'ratio': highest_ratio, 'another_names': another_names})
            return return_res


    @http.route('/create_record', auth='user', type="json")
    def create_record(self, **kw):
        result = request.jsonrequest
        if result['params']:
            request_body = result['params']
            model = request_body['model']
            values = request_body['values'] if 'values' in request_body else {}
            record = request.env[model].create(values)
            return {
                "result": {
                    "code": 200,
                    "message": 'SUCCESS_CREATING_RECORD',
                    "result":record.sudo().read(),
                    "success": True
                }
            }
        else:
            return result['error']


    @http.route('/v2/create_multi_orders', auth='user', type="json")
    def create_multi_orders_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args
        inclusive_delivery = business.inclusive_delivery
        if request.jsonrequest:
            if rec['orders_list']:
                orders_recs = rec['orders_list']
                IDs = []
                sequences = []
                references = []
                fail_messages = []
                for rec in orders_recs:
                    if not 'cost' in rec:
                        args = {'code':401,'fail': True, 'message': _('Failed to Add orders please add value of cost')}
                    else:
                        vals = rec
                        if inclusive_delivery:
                            vals['copy_total_cost'] = rec['cost']
                        try:
                            new_order = request.env['rb_delivery.order'].create(vals)
                            new_order_id = new_order.id
                            new_order_seq = new_order.sequence
                            new_order_ref = new_order.reference_id
                        except Exception as e:
                            new_order_id = ""
                            new_order_seq = ""
                            fail_message = ''
                            if 'reference_id' in vals and vals['reference_id']:
                                fail_message = _('Order of sequence ')+str(vals['reference_id']) + _(' failed to be added because of ') + str(e)
                            else:
                                fail_message = str(e)
                            fail_messages.append(fail_message)
                        if new_order_id != "" and new_order_seq != "":
                            IDs.append(new_order_id)
                            sequences.append(new_order_seq)
                            references.append(new_order_ref)


                if len(IDs)>0 and len(sequences)>0:
                    args = {'code':200,'success': True, 'message': _('Success Add orders'), 'IDs': IDs, 'Sequences':sequences, 'References':references }
                    if len(fail_messages)>0:
                        args['fail_message'] = fail_messages
                else:
                    args = {'code':401,'fail': True, 'message': fail_messages}
        return args

    @http.route('/create_multi_orders_super', auth='none', type="json")
    def create_multi_orders_super(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        super_manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not super_manager:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args
        if request.jsonrequest:
            if rec['orders_list']:
                orders_recs = rec['orders_list']
                IDs = []
                sequences = []
                references = []
                fail_messages = []
                for rec in orders_recs:
                    inclusive_delivery = False
                    if 'sender_id' in rec:
                        user = request.env['rb_delivery.user'].sudo().search([('id','=',rec['sender_id'])])
                        if user.id:
                            inclusive_delivery = user.inclusive_delivery
                            user_id = user.id
                        else:
                            user_mob = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',rec['sender_id'])])
                            if user_mob.id:
                                inclusive_delivery = user_mob.inclusive_delivery
                                user_id = user_mob.id
                            else:
                                return {'status':403,'message':'ID/Mobile number entered is wrong'}
                    else:
                        return {'status':403,'message':'Please add sender_id'}

                    if not 'cost' in rec:
                        args = {'code':200,'success': True, 'message': 'Fail Add orders, please add total_cost value'}
                    vals = rec
                    if inclusive_delivery:
                        vals['copy_total_cost'] = rec['cost']
                        vals['assign_to_business'] = user_id
                    try:
                        new_order = request.env['rb_delivery.order'].create(vals)
                        new_order_id = new_order.id
                        new_order_seq = new_order.sequence
                        new_order_ref = new_order.reference_id
                    except Exception as e:
                        new_order_id = ""
                        new_order_seq = ""
                        fail_message = ''
                        if 'reference_id' in vals and vals['reference_id']:
                            fail_message = _('Order of sequence ')+str(vals['reference_id']) + _(' failed to be added because of ') + str(e)
                        else:
                            fail_message = str(e)
                        fail_messages.append(fail_message)
                    if new_order_id != "" and new_order_seq != "":
                        IDs.append(new_order_id)
                        sequences.append(new_order_seq)
                        references.append(new_order_ref)

                        #IDs.append(new_order_id)

                if len(IDs)>0 and len(sequences)>0:
                    args = {'code':200,'success': True, 'message': _('Success Add orders'), 'IDs': IDs, 'Sequences':sequences, 'References':references }
                    if len(fail_messages)>0:
                        args['fail_message'] = fail_messages
                else:
                    args = {'code':401,'fail': True, 'message': fail_messages}
        return args

    @http.route('/v2/create_multi_orders_super', auth='user', type="json")
    def create_multi_orders_super_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        super_manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not super_manager:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args
        if request.jsonrequest:
            if rec['orders_list']:
                orders_recs = rec['orders_list']
                IDs = []
                sequences = []
                references = []
                fail_messages = []
                for rec in orders_recs:
                    inclusive_delivery = False
                    if 'sender_id' in rec:
                        user = request.env['rb_delivery.user'].sudo().search([('id','=',rec['sender_id'])])
                        if user.id:
                            inclusive_delivery = user.inclusive_delivery
                            user_id = user.id
                        else:
                            user_mob = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',rec['sender_id'])])
                            if user_mob.id:
                                inclusive_delivery = user_mob.inclusive_delivery
                                user_id = user_mob.id
                            else:
                                return {'status':403,'message':'ID/Mobile number entered is wrong'}
                    else:
                        return {'status':403,'message':'Please add sender_id'}

                    if not 'cost' in rec:
                        args = {'code':200,'success': True, 'message': 'Fail Add orders, please add total_cost value'}
                    vals = rec
                    if inclusive_delivery:
                        vals['copy_total_cost'] = rec['cost']
                        vals['assign_to_business'] = user_id
                        try:
                            new_order = request.env['rb_delivery.order'].create(vals)
                            new_order_id = new_order.id
                            new_order_seq = new_order.sequence
                            new_order_ref = new_order.reference_id
                        except Exception as e:
                            new_order_id = ""
                            new_order_seq = ""
                            fail_message = ''
                            if 'reference_id' in vals and vals['reference_id']:
                                fail_message = _('Order of sequence ')+str(vals['reference_id']) + _(' failed to be added because of ') + str(e)
                            else:
                                fail_message = str(e)
                            fail_messages.append(fail_message)
                        if new_order_id != "" and new_order_seq != "":
                            IDs.append(new_order_id)
                            sequences.append(new_order_seq)
                            references.append(new_order_ref)

                        #IDs.append(new_order_id)

                if len(IDs)>0 and len(sequences)>0:
                    args = {'code':200,'success': True, 'message': _('Success Add orders'), 'IDs': IDs, 'Sequences':sequences, 'References':references }
                    if len(fail_messages)>0:
                        args['fail_message'] = fail_messages
                else:
                    args = {'code':401,'fail': True, 'message': fail_messages}
        return args

    @http.route('/v2/create_multi_orders_super', auth='user', type="json")
    def create_multi_orders_super_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        super_manager = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not super_manager:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args
        if request.jsonrequest:
            if rec['orders_list']:
                orders_recs = rec['orders_list']
                IDs = []
                sequences = []
                references = []
                fail_messages = []
                for rec in orders_recs:
                    inclusive_delivery = False
                    if 'sender_id' in rec:
                        user = request.env['rb_delivery.user'].sudo().search([('id','=',rec['sender_id'])])
                        if user.id:
                            inclusive_delivery = user.inclusive_delivery
                            user_id = user.id
                        else:
                            user_mob = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',rec['sender_id'])])
                            if user_mob.id:
                                inclusive_delivery = user_mob.inclusive_delivery
                                user_id = user_mob.id
                            else:
                                return {'status':403,'message':'ID/Mobile number entered is wrong'}
                    else:
                        return {'status':403,'message':'Please add sender_id'}

                    if not 'cost' in rec:
                        args = {'code':200,'success': True, 'message': 'Fail Add orders, please add total_cost value'}
                    vals = rec
                    if inclusive_delivery:
                        vals['copy_total_cost'] = rec['cost']
                        vals['assign_to_business'] = user_id
                        try:
                            new_order = request.env['rb_delivery.order'].create(vals)
                            new_order_id = new_order.id
                            new_order_seq = new_order.sequence
                            new_order_ref = new_order.reference_id
                        except Exception as e:
                            new_order_id = ""
                            new_order_seq = ""
                            fail_message = ''
                            if 'reference_id' in vals and vals['reference_id']:
                                fail_message = _('Order of sequence ')+str(vals['reference_id']) + _(' failed to be added because of ') + str(e)
                            else:
                                fail_message = str(e)
                            fail_messages.append(fail_message)
                        if new_order_id != "" and new_order_seq != "":
                            IDs.append(new_order_id)
                            sequences.append(new_order_seq)
                            references.append(new_order_ref)

                        #IDs.append(new_order_id)

                if len(IDs)>0 and len(sequences)>0:
                    args = {'code':200,'success': True, 'message': _('Success Add orders'), 'IDs': IDs, 'Sequences':sequences, 'References':references }
                    if len(fail_messages)>0:
                        args['fail_message'] = fail_messages
                else:
                    args = {'code':401,'fail': True, 'message': fail_messages}
        return args

    @http.route('/returned_request', auth='none', type="json")
    def returned_request_order(self, **rec):
        if 'db' in http.request.params and 'password' in http.request.params and 'login' in http.request.params:
            db = http.request.params['db']
            password = http.request.params['password']
            login = http.request.params['login']
            del rec['login']
            del rec['password']
            del rec['db']
            # authenticate
            uid = request.session.authenticate(db, login, password)

        else:
            # authenticate
            uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args
        if request.jsonrequest:
            if rec['order_id']:
                order_id = rec['order_id']
                vals = {'state': 'returned_request'}
                order_rec = request.env['rb_delivery.order'].search([('sequence', '=', order_id)],limit=1)
                if order_rec.id:
                    order_rec.write(vals)
                    args = {'code':200,'success': True, 'message': 'Returned Request Sent', 'id': order_rec.id }
                else:
                    args = {'code':401,'message':'No order was found'}
        return args

    @http.route('/edit_order', auth='none', type="json")
    def edit_order(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        user = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])
        is_business = False
        if not user:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args
        if user.role_code == 'rb_delivery.role_business':
            is_business = True

        if request.jsonrequest:
            if rec['order_id']:
                order_id = rec['order_id']
                vals = rec['vals']
                order_model = request.env['rb_delivery.order']
                order = order_model.search([('state','!=','deleted'),'|','|',('sequence', '=', str(order_id)),('reference_id', '=', str(order_id)),('partner_reference_id', '=', str(order_id))])
                if not order:
                    if is_business:
                        domain = [('state','!=','deleted'),('assign_to_business','=',user.id),'|','|',('sequence', '=', str(order_id)),('reference_id', '=', str(order_id)),('partner_reference_id', '=', str(order_id))]
                        
                    else:
                        domain = [('state','!=','deleted'),'|','|',('sequence', '=', str(order_id)),('reference_id', '=', str(order_id)),('partner_reference_id', '=', str(order_id))]
                    order = order_model.sudo().search(domain)
                    if order:
                        args = {'code':401,'fail': True, 'message': _('Fail Edit, user does not have access to order '+str(order_id))}
                        return args
                    if isinstance(order_id, int) and len(str(order_id)) < 10:
                        order = order_model.search([('state','!=','deleted'),('id', '=', order_id)])
                        if not order:
                            order = order_model.sudo().search([('state','!=','deleted'),('id', '=', order_id)])
                            if order:
                                args = {'code':401,'fail': True, 'message': _('Fail Edit, user does not have access to order '+str(order_id))}
                                return args
                            
                            args = {'code':401,'fail': True, 'message': _('Fail Edit, order id/sequence is not valid')}
                            return args
                    else:
                        args = {'code':401,'fail': True, 'message': _('Fail Edit, order id/sequence is not valid')}
                        return args
                try:
                    if len(order)>1:
                        order_seqs = ', '.join([str(ord.sequence) for ord in order])
                        message = _('There are more than one order with the same reference %s to sequences %s, please use the sequence instead.')%(str(order_id),order_seqs)
                        args = {'code':401,'success': False, 'message': message}
                        return args
                    if 'customer_area' in vals and vals['customer_area'] == '':
                        vals.pop('customer_area', None)
                    if 'customer_sub_area' in vals and vals['customer_sub_area'] == '':
                        vals.pop('customer_sub_area', None)
                    order.write(vals)
                    order.with_context(original_uid=request.env.user.partner_id.id).sudo().message_post(body=_("Order has been edited through edit_order API"))
                    args = {'code':200,'success': True, 'message': 'Success Edit', 'id': order.sudo().id }
                except Exception as e:
                    fail_message = _("Order failed to be edited because of ") + str(e)
                    args = {'code':401,'fail': True, 'message': fail_message}

            else:
                args = {'code':401,'message':_('Please add order ID')}
        return args

    @http.route('/v2/edit_order', auth='user', type="json")
    def edit_order_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args
        if request.jsonrequest:
            if rec['order_id']:
                order_id = rec['order_id']
                vals = rec['vals']
                order_rec_seq = request.env['rb_delivery.order'].search([('sequence', '=', str(order_id))])
                if order_rec_seq.id:
                    try:
                        order_rec_seq.write(vals)
                        args = {'code':200,'success': True, 'message': 'Success Edit', 'id': order_rec_seq.id }
                    except Exception as e:
                        fail_message = _("Order failed to be edited because of ") + str(e)
                        args = {'code':401,'fail': True, 'message': fail_message}
                else:
                    order_rec_ref = request.env['rb_delivery.order'].search([('reference_id', '=', str(order_id))])
                    if order_rec_ref.id:
                        try:
                            order_rec_ref.write(vals)
                            args = {'code':200,'success': True, 'message': 'Success Edit', 'id': order_rec_ref.id }
                        except Exception as e:
                            fail_message = _("Order failed to be edited because of ") + str(e)
                            args = {'code':401,'fail': True, 'message': fail_message}
                    else:
                        order_rec= request.env['rb_delivery.order'].search([('id', '=', order_id)])
                        if order_rec.id:
                            try:
                                order_rec.write(vals)
                                args = {'code':200,'success': True, 'message': 'Success Edit', 'id': order_rec.id }
                            except Exception as e:
                                fail_message = _("Order failed to be edited because of ") + str(e)
                                args = {'code':401,'fail': True, 'message': fail_message}
                        else:
                            args = {'code':401,'fail': True, 'message': _('Fail Edit, order id/sequence is not valid')}
            else:
                args = {'code':401,'message':'Please add order ID'}
        return args

    @http.route('/get_order_status', auth='none', type="json")
    def get_order_status(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args
        if request.jsonrequest:
            if 'order_id' not in rec:
                return {'status':403,'message':'Please add value of order_id'}
            order_id = rec['order_id']
            vals = {}
            order_rec_seq = request.env['rb_delivery.order'].search([('sequence', '=', str(order_id))])
            if order_rec_seq.id:
                vals = {
                    'id': order_rec_seq.id,
                    'sequence':order_rec_seq.sequence,
                    'status': order_rec_seq.state}
                data = {'status': 200, 'response': vals, 'message': 'Success' }
            else:
                order_rec_ref = request.env['rb_delivery.order'].search([('reference_id', '=', str(order_id))])
                if order_rec_ref.id:
                    vals = {
                        'id': order_rec_ref.id,
                        'sequence':order_rec_ref.sequence,
                        'status': order_rec_ref.state}
                    data = {'status': 200, 'response': vals, 'message': 'Success' }
                else:
                    order_rec = request.env['rb_delivery.order'].search([('id', '=', order_id)])
                    if order_rec.id:
                        vals = {
                            'id': order_rec.id,
                            'sequence':order_rec.sequence,
                            'status': order_rec.state}
                        data = {'status': 200, 'response': vals, 'message': 'Success' }
                    else:
                        data = {'status': 403,'message': 'Order Id/ Sequence does not exist in the system.' }
        return data

    @http.route('/v2/get_order_status', auth='user', type="json")
    def get_order_status_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args
        if request.jsonrequest:
            if 'order_id' not in rec:
                return {'status':403,'message':'Please add value of order_id'}
            order_id = rec['order_id']
            vals = {}
            order_rec_seq = request.env['rb_delivery.order'].search([('sequence', '=', str(order_id))])
            if order_rec_seq.id:
                vals = {
                    'id': order_rec_seq.id,
                    'sequence':order_rec_seq.sequence,
                    'status': order_rec_seq.state}
                data = {'status': 200, 'response': vals, 'message': 'Success' }
            else:
                order_rec_ref = request.env['rb_delivery.order'].search([('reference_id', '=', str(order_id))])
                if order_rec_ref.id:
                    vals = {
                        'id': order_rec_ref.id,
                        'sequence':order_rec_ref.sequence,
                        'status': order_rec_ref.state}
                    data = {'status': 200, 'response': vals, 'message': 'Success' }
                else:
                    order_rec = request.env['rb_delivery.order'].search([('id', '=', order_id)])
                    if order_rec.id:
                        vals = {
                            'id': order_rec.id,
                            'sequence':order_rec.sequence,
                            'status': order_rec.state}
                        data = {'status': 200, 'response': vals, 'message': 'Success' }
                    else:
                        data = {'status': 403,'message': 'Order Id/ Sequence does not exist in the system.' }
        return data

    @http.route('/get_order_agent', auth='none', type="json")
    def get_order_agent(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User '+str(login)+' is not delivery user.')}
            return args
        if request.jsonrequest:
            if 'order_id' not in rec:
                return {'status': 403,'message': 'Please add value of order_id' }
            order_id = rec['order_id']
            vals = {}
            order_rec_seq = request.env['rb_delivery.order'].search([('sequence', '=', str(order_id))])
            if order_rec_seq.id:
                if order_rec_seq.assign_to_agent:
                    agent = request.env['rb_delivery.user'].sudo().search([('id','=',order_rec_seq.assign_to_agent.id)])
                    vals = {
                        'Agent ID': agent.id,
                        'Agent Name': agent.username,
                        'Agent Mobile Number':agent.mobile_number,
                        'Agent Area':agent.area_id.name,
                        'Agent Address':agent.address}
                    data = {'status': 200, 'response': vals, 'message': 'Success' }
                else:
                    data = {'status': 200, 'message': 'Order has no agent' }
            else:
                order_rec_ref = request.env['rb_delivery.order'].search([('reference_id', '=', str(order_id))])
                if order_rec_ref.id:
                    if order_rec_ref.assign_to_agent:
                        agent = request.env['rb_delivery.user'].sudo().search([('id','=',order_rec_ref.assign_to_agent.id)])
                        vals = {
                            'Agent ID': agent.id,
                            'Agent Name': agent.username,
                            'Agent Mobile Number':agent.mobile_number,
                            'Agent Area':agent.area_id.name,
                            'Agent Address':agent.address}
                        data = {'status': 200, 'response': vals, 'message': 'Success' }
                    else:
                        data = {'status': 200, 'message': 'Order has no agent' }
                else:
                    order_rec = request.env['rb_delivery.order'].search([('id', '=', order_id)])
                    if order_rec.id:
                        if order_rec.assign_to_agent:
                            agent = request.env['rb_delivery.user'].sudo().search([('id','=',order_rec.assign_to_agent.id)])
                            vals = {
                                'Agent ID': agent.id,
                                'Agent Name': agent.username,
                                'Agent Mobile Number':agent.mobile_number,
                                'Agent Area':agent.area_id.name,
                                'Agent Address':agent.address}
                            data = {'status': 200, 'response': vals, 'message': 'Success' }
                        else:
                           data = {'status': 200, 'message': 'Order has no agent' }
                    else:
                        data = {'status': 200, 'message': 'There is no order with same Reference ID/Sequence' }
        return data

    @http.route('/v2/get_order_agent', auth='user', type="json")
    def get_order_agent_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args
        if request.jsonrequest:
            if 'order_id' not in rec:
                return {'status': 403,'message': 'Please add value of order_id' }
            order_id = rec['order_id']
            vals = {}
            order_rec_seq = request.env['rb_delivery.order'].search([('sequence', '=', str(order_id))])
            if order_rec_seq.id:
                if order_rec_seq.assign_to_agent:
                    agent = request.env['rb_delivery.user'].sudo().search([('id','=',order_rec_seq.assign_to_agent.id)])
                    vals = {
                        'Agent ID': agent.id,
                        'Agent Name': agent.username,
                        'Agent Mobile Number':agent.mobile_number,
                        'Agent Area':agent.area_id.name,
                        'Agent Address':agent.address}
                    data = {'status': 200, 'response': vals, 'message': 'Success' }
                else:
                    data = {'status': 200, 'message': 'Order has no agent' }
            else:
                order_rec_ref = request.env['rb_delivery.order'].search([('reference_id', '=', str(order_id))])
                if order_rec_ref.id:
                    if order_rec_ref.assign_to_agent:
                        agent = request.env['rb_delivery.user'].sudo().search([('id','=',order_rec_ref.assign_to_agent.id)])
                        vals = {
                            'Agent ID': agent.id,
                            'Agent Name': agent.username,
                            'Agent Mobile Number':agent.mobile_number,
                            'Agent Area':agent.area_id.name,
                            'Agent Address':agent.address}
                        data = {'status': 200, 'response': vals, 'message': 'Success' }
                    else:
                        data = {'status': 200, 'message': 'Order has no agent' }
                else:
                    order_rec = request.env['rb_delivery.order'].search([('id', '=', order_id)])
                    if order_rec.id:
                        if order_rec.assign_to_agent:
                            agent = request.env['rb_delivery.user'].sudo().search([('id','=',order_rec.assign_to_agent.id)])
                            vals = {
                                'Agent ID': agent.id,
                                'Agent Name': agent.username,
                                'Agent Mobile Number':agent.mobile_number,
                                'Agent Area':agent.area_id.name,
                                'Agent Address':agent.address}
                            data = {'status': 200, 'response': vals, 'message': 'Success' }
                        else:
                           data = {'status': 200, 'message': 'Order has no agent' }
                    else:
                        data = {'status': 200, 'message': 'There is no order with same Reference ID/Sequence' }
        return data

    @http.route('/order', auth='none', type="json", save_session=False)
    def order(self, **rec):
        db = http.request.params.get('db')
        password = http.request.params.get('password')
        login = http.request.params.get('login')

        uid = request.session.authenticate(db, login, password)

        for key in ['login', 'password', 'db']:
            rec.pop(key, None)

        if not uid:
            return {'code': 403, 'fail': True, 'message': _('Failed to authenticate.')}

        business = request.env['rb_delivery.user'].sudo().search([('user_id', '=', uid)])
        if not business:
            return {'code': 401, 'fail': True, 'message': _('User ' + str(login) + ' is not delivery user.')}

        responses = []
        confs = request.env['rb_delivery.client_configuration'].sudo().get_param([
            'waybill_in_api', 'return_waybill_in_get_order_details_api', 'order_details_api_fields'
        ])
        if request.jsonrequest:
            if 'order_ids' not in rec:
                return {'status': 403, 'message': 'Please add Order IDs'}
            order_ids = rec['order_ids']
            company = http.request.env['res.company'].sudo().search_read([], ['base_url'])[0]

            waybill = (
                request.env['ir.actions.report'].sudo().browse([confs.get('waybill_in_api')]).report_name
                if confs.get('waybill_in_api')
                else 'rb_delivery.report_rb_delivery_order_detail_a4_action'
            )
            return_waybill_in_get_order_details_api = confs.get('return_waybill_in_get_order_details_api')

            order_field_ids = confs.get('order_details_api_fields', [])
            order_fields = request.env['ir.model.fields'].sudo().browse(order_field_ids)
            order_field_defs = []
            for field in order_fields:
                order_field_defs.append({
                    'name': field.name,
                    'ttype': field.ttype,
                    'relation': field.relation,
                })

            for order_id in order_ids:
                vals = {}
                order = request.env['rb_delivery.order'].search([('sequence', '=', str(order_id))], limit=1)
                if not order:
                    order = request.env['rb_delivery.order'].search(
                        [('reference_id', '=', str(order_id)), ('state', '!=', 'deleted')], limit=1
                    )
                if not order:
                    order = request.env['rb_delivery.order'].browse(order_id)

                if order and order.id:
                    order_sudo = order.sudo()
                    for field_def in order_field_defs:
                        field_name = field_def['name']
                        if field_name == "assign_to_agent":
                            if order_sudo.assign_to_agent:
                                vals['has_driver'] = True
                                vals['driver_name'] = order_sudo.assign_to_agent.username
                                vals['driver_mobile_number'] = order_sudo.assign_to_agent.mobile_number
                            else:
                                vals['has_driver'] = False
                        else:
                            if field_def['ttype'] in ["many2many", "one2many"]:
                                records_list = []
                                for record in order[field_name]:
                                    if field_def['relation'] == "rb_delivery.user":
                                        records_list.append(record.username)
                                    elif field_def['relation'] == "rb_delivery.order":
                                        records_list.append(record.sequence)
                                    else:
                                        records_list.append(record.name)
                                vals[field_name] = records_list
                            elif field_def['ttype'] == "many2one":
                                related_record = order[field_name]
                                if related_record:
                                    if field_def['relation'] == "rb_delivery.user":
                                        vals[field_name] = related_record.username
                                    elif field_def['relation'] == "rb_delivery.order":
                                        vals[field_name] = related_record.sequence
                                    else:
                                        vals[field_name] = related_record.name
                                else:
                                    vals[field_name] = ""
                            else:
                                vals[field_name] = order[field_name] or ""
                    if company.get("base_url") and waybill and return_waybill_in_get_order_details_api:
                        attachment = request.env['ir.attachment'].sudo().create({
                            'name': 'waybill.pdf',
                            'datas_fname': 'waybill.pdf',
                            'res_model': 'rb_delivery.order',
                            'public': True
                        })
                        request.env['rb_delivery.order'].create_pdf_report([order.id], attachment, waybill)
                        pdf_link = '/web/content/%s' % attachment.id
                        vals['waybill_url'] = company.get("base_url") + pdf_link

                    responses.append(vals)
                else:
                    responses.append({
                        "message": _('Order Sequence/Reference ID %s does not exist, or you have no access to this order.') % order_id
                    })
        if not responses:
            responses.append({"message": _('No response')})
        return {'status': 200, 'response': responses, 'message': 'success'}


    @http.route('/get_order_detail', auth='none', type="json",save_session=False)
    def get_order_detail(self, **rec):
        data = {
            'status': 403,
            'message': _(
                "Access Denied: Please use our updated API, which supports bulk processing of order sequences/reference IDs "
                "instead of handling a single order at a time. You can find the updated API documentation here: "
                "https://documenter.getpostman.com/view/14960946/2sAYdiopYL"
            )
        }
        return data

    @http.route('/v2/get_order_detail', auth='user', type="json")
    def get_order_detail_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        business = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if not business:
            args = {'code':401,'fail': True, 'message': _('User is not delivery user.')}
            return args
        if request.jsonrequest:
            if 'order_id' not in rec:
                data = {'status': 403, 'message': 'Please add Order ID' }
            order_id = rec['order_id']
            vals = {}
            order_rec_seq = request.env['rb_delivery.order'].search([('sequence', '=', str(order_id))])
            if order_rec_seq.id:
                vals = {
                    'id': order_rec_seq.id,
                    'Status': order_rec_seq.state,
                    'Customer Address': order_rec_seq.customer_address,
                    'Customer Mobile': order_rec_seq.customer_mobile,
                    'Customer Name': order_rec_seq.customer_name,
                    'Customer Area': order_rec_seq.customer_area.name
                }
                data = {'status': 200, 'response': vals, 'message': 'Success' }
            else:
                order_rec_ref = request.env['rb_delivery.order'].search([('reference_id', '=', str(order_id))])
                if order_rec_ref.id:
                    vals = {
                        'id': order_rec_ref.id,
                        'Status': order_rec_ref.state,
                        'Customer Address': order_rec_ref.customer_address,
                        'Customer Mobile': order_rec_ref.customer_mobile,
                        'Customer Name': order_rec_ref.customer_name,
                        'Customer Area': order_rec_ref.customer_area.name
                    }
                    data = {'status': 200, 'response': vals, 'message': 'Success' }
                else:
                    order_rec = request.env['rb_delivery.order'].search([('id', '=', order_id)])
                    if order_rec.id:
                        vals = {
                            'id': order_rec.id,
                            'Status': order_rec.state,
                            'Customer Address': order_rec.customer_address,
                            'Customer Mobile': order_rec.customer_mobile,
                            'Customer Name': order_rec.customer_name,
                            'Customer Area': order_rec.customer_area.name
                        }
                        data = {'status': 200, 'response': vals, 'message': 'Success' }
                    else:
                       data = {'status': 403, 'message': 'Order Sequence/Reference ID is wrong' }
        return data

    @http.route('/get_delivery_cost', auth='none', type="json")
    def get_delivery_cost(self, **rec):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del rec['login']
        del rec['password']
        del rec['db']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        user = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])
        sender_id = None
        if 'sender_id' in http.request.params and http.request.params['sender_id']:
            sender_id = http.request.params['sender_id']
        user = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])
        if sender_id:
            user = request.env['rb_delivery.user'].sudo().search([('id','=',sender_id)])

        if request.jsonrequest:
            if user.id:
                if 'customer_area' in rec and rec['customer_area'] and 'order_type_id' in rec and rec['order_type_id']:
                    customer_area = rec['customer_area']
                    order_type_id = rec['order_type_id']
                    if str(customer_area).isnumeric():
                        area = request.env['rb_delivery.area'].search([('id','=',customer_area)])
                        if area and area.id:
                            customer_area = area.id
                        else:
                            area = request.env['rb_delivery.area'].search([('code','=',customer_area)])
                            if area and area.id:
                                customer_area = area.id
                            else:
                                args = {'status': 401, 'message': _('Area '+ customer_area + ' does not exist.') }
                                return args
                    else:
                        area = request.env['rb_delivery.area'].search(['|',('name','=',customer_area),('code','=',customer_area)])
                        if area and area.id:
                            customer_area = area.id
                        else:
                            args = {'status': 401, 'message': _('Area '+ customer_area + ' does not exist.') }
                            return args
                    if str(order_type_id).isnumeric():
                        order_type = request.env['rb_delivery.order_type'].search([('id','=',order_type_id)])
                        if order_type and order_type.id:
                            order_type_id = order_type.id
                        else:
                            order_type = request.env['rb_delivery.order_type'].search([('code','=',order_type_id)])
                            if order_type and order_type.id:
                                order_type_id = order_type.id
                            else:
                                args = {'status': 401, 'message': _('Order type '+ order_type_id + ' does not exist.') }
                                return args
                    else:
                        order_type = request.env['rb_delivery.order_type'].search(['|',('name','=',order_type_id),('code','=',order_type_id)])
                        if order_type and order_type.id:
                            order_type_id = order_type.id
                        else:
                            args = {'status': 401, 'message': _('Order type '+ order_type_id + ' does not exist.') }
                            return args

                    data = {
                        'sender_id': user.id,
                        'to_area_id': customer_area,
                        'order_type_id': order_type_id}
                    if 'business_alt_area' in rec and rec['business_alt_area']:
                        business_alt_area = rec['business_alt_area']
                        if str(business_alt_area).isnumeric():
                            area = request.env['rb_delivery.area'].search([('id','=',business_alt_area)])
                            if area and area.id:
                                business_alt_area = area.id
                            else:
                                area = request.env['rb_delivery.area'].search([('code','=',business_alt_area)])
                                if area and area.id:
                                    business_alt_area = area.id
                                else:
                                    args = {'status': 401, 'message': _('Area '+ business_alt_area + ' does not exist.') }
                                    return args
                        else:
                            area = request.env['rb_delivery.area'].search(['|',('name','=',business_alt_area),('code','=',business_alt_area)])
                            if area and area.id:
                                business_alt_area = area.id
                            else:
                                args = {'status': 401, 'message': _('Area '+ business_alt_area + ' does not exist.') }
                                return args
                        data['sender_area']=business_alt_area
                    cost = request.env['rb_delivery.pricelist'].get_price(data)

                    args = {'status': 200, 'delivery_cost': cost, 'message': 'success' }
                else:
                    args = {'status':401,'message':_("Please add order_type_id and customer_area.")}
        return args

    @http.route('/v2/get_delivery_cost', auth='user', type="json")
    def get_delivery_cost_v2(self, **rec):
        # authenticate
        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        user = request.env['rb_delivery.user'].sudo().search([('user_id','=',uid)])

        if request.jsonrequest:
            if 'sender_id' in rec and rec['sender_id']:
                sender_id = rec['sender_id']
                business = request.env['rb_delivery.user'].search(['|','|',('username','=',sender_id),('mobile_number','=',sender_id),('commercial_name','=',sender_id)])
                if not business:
                    args = {'status': 401, 'message': _('User '+ str(sender_id) + ' does not exist.') }
                    return args
                if 'customer_area' in rec and rec['customer_area'] and 'order_type_id' in rec and rec['order_type_id']:
                    customer_area = rec['customer_area']
                    order_type_id = rec['order_type_id']
                    if str(customer_area).isnumeric():
                        area = request.env['rb_delivery.area'].search([('id','=',customer_area)])
                        if area and area.id:
                            customer_area = area.id
                        else:
                            area = request.env['rb_delivery.area'].search([('code','=',customer_area)])
                            if area and area.id:
                                customer_area = area.id
                            else:
                                args = {'status': 401, 'message': _('Area '+ customer_area + ' does not exist.') }
                                return args
                    else:
                        area = request.env['rb_delivery.area'].search(['|',('name','=',customer_area),('code','=',customer_area)])
                        if area and area.id:
                            customer_area = area.id
                        else:
                            args = {'status': 401, 'message': _('Area '+ customer_area + ' does not exist.') }
                            return args
                    if str(order_type_id).isnumeric():
                        order_type = request.env['rb_delivery.order_type'].search([('id','=',order_type_id)])
                        if order_type and order_type.id:
                            order_type_id = order_type.id
                        else:
                            order_type = request.env['rb_delivery.order_type'].search([('code','=',order_type_id)])
                            if order_type and order_type.id:
                                order_type_id = order_type.id
                            else:
                                args = {'status': 401, 'message': _('Order type '+ order_type_id + ' does not exist.') }
                                return args
                    else:
                        order_type = request.env['rb_delivery.order_type'].search(['|',('name','=',order_type_id),('code','=',order_type_id)])
                        if order_type and order_type.id:
                            order_type_id = order_type.id
                        else:
                            args = {'status': 401, 'message': _('Order type '+ order_type_id + ' does not exist.') }
                            return args
                    data = {
                        'sender_id': business.id,
                        'to_area_id': rec['customer_area'],
                        'order_type_id': rec['order_type_id']}
                    cost = request.env['rb_delivery.pricelist'].get_price(data)

                    args = {'status': 200, 'Delivery cost': cost, 'message': 'success' }
                else:
                    args = {'status':401,'message':_("Please add order_type_id and customer_area.")}
                    return args
            elif user.id:
                if 'customer_area' in rec and rec['customer_area'] and 'order_type_id' in rec and rec['order_type_id']:
                    customer_area = rec['customer_area']
                    order_type_id = rec['order_type_id']
                    if str(customer_area).isnumeric():
                        area = request.env['rb_delivery.area'].search([('id','=',customer_area)])
                        if area and area.id:
                            customer_area = area.id
                        else:
                            area = request.env['rb_delivery.area'].search([('code','=',customer_area)])
                            if area and area.id:
                                customer_area = area.id
                            else:
                                args = {'status': 401, 'message': _('Area '+ customer_area + ' does not exist.') }
                                return args
                    else:
                        area = request.env['rb_delivery.area'].search(['|',('name','=',customer_area),('code','=',customer_area)])
                        if area and area.id:
                            customer_area = area.id
                        else:
                            args = {'status': 401, 'message': _('Area '+ customer_area + ' does not exist.') }
                            return args
                    if str(order_type_id).isnumeric():
                        order_type = request.env['rb_delivery.order_type'].search([('id','=',order_type_id)])
                        if order_type and order_type.id:
                            order_type_id = order_type.id
                        else:
                            order_type = request.env['rb_delivery.order_type'].search([('code','=',order_type_id)])
                            if order_type and order_type.id:
                                order_type_id = order_type.id
                            else:
                                args = {'status': 401, 'message': _('Order type '+ order_type_id + ' does not exist.') }
                                return args
                    else:
                        order_type = request.env['rb_delivery.order_type'].search(['|',('name','=',order_type_id),('code','=',order_type_id)])
                        if order_type and order_type.id:
                            order_type_id = order_type.id
                        else:
                            args = {'status': 401, 'message': _('Order type '+ order_type_id + ' does not exist.') }
                            return args

                    data = {
                        'sender_id': user.id,
                        'to_area_id': customer_area,
                        'order_type_id': order_type_id}
                    cost = request.env['rb_delivery.pricelist'].get_price(data)

                    args = {'status': 200, 'Delivery cost': cost, 'message': 'success' }
                else:
                    args = {'status':401,'message':_("Please add order_type_id and customer_area.")}
        return args



    # @http.route('/delete_order', auth='user', type="json")
    # def delete_order(self, **rec):
    #     if request.jsonrequest:
    #         if rec['order_id']:
    #             order_id = rec['order_id']
    #             order_rec = request.env['rb_delivery.order'].search([('id', '=', order_id)])
    #             if order_rec.id and order_rec.state == 'waiting':
    #                 order_rec.unlink()
    #                 args = {'success': True, 'Message': 'Success Delete', 'id': order_rec.id }
    #             else:
    #                 args = {'success': False, 'Message': 'Failed Delete', 'id': order_rec.id }
    #     return args


    @http.route('/create_company_order', auth='user', type="json")
    def create_company_order(self, **rec):
        if request.jsonrequest:
            order_seq = rec['order_seq']
            order = request.env['rb_delivery.order'].sudo().search([('sequence','=',order_seq)],limit=1)
            if request.uid:
                user = request.env['res.users'].search([('id', '=', request.uid)])
                driver = request.env['rb_delivery.user'].search([('user_id','=',user.id)])
                is_driver = user.has_group('rb_delivery.role_driver') or user.has_group('rb_delivery.role_sort_and_distribute_representative') or user.has_group('rb_delivery.role_picking_up_representative')

                if is_driver:
                    if order.id:
                        if order.state=='in_branch':
                            message = _("Order has been updated from another company %s, through API Integration.")%(driver.username)
                            data = {'uid':request.uid,'message':message,'records':order,'values':{'state': 'in_progress', 'assign_to_agent': driver.id},'update':False}
                            request.env['rb_delivery.utility'].olivery_sudo(data)
                            args = {'code':200,'success': True, 'message': 'Success change and assign', 'id': order.id }
                    else:
                        result = request.env['rb_delivery.order'].create({
                            'reference_id' : order_seq,
                            'sequence': order_seq,
                            'state': 'in_progress'
                            })
                        args = {'code':200,'success': True, 'message': 'Success create and assign', 'id': result.id }
                else:
                    args = {'code':401,'message':'User is not a driver'}

    @http.route('/olivery/sign_up', type='http', methods=['POST', 'GET'], auth='public', website=True, csrf=False,save_session=False)
    def olivery_signup(self, **req):
        # res = request.env['res.groups'].sudo().search([('name', '=', 'Business Role')])
        res = request.env.ref('rb_delivery.role_business')
        if req.get('password') == req.get('confirm_password'):
            result = request.env['rb_delivery.user'].sudo().create({
                'password': req.get('password'),
                'username': req.get('username'),
                'address': req.get('address'),
                'area_id': req.get('area_id'),
                'email': req.get('email'),
                'mobile_number':req.get('mobile_number'),
                'commercial_name':req.get('commercial_name'),
                'commercial_activity':req.get('commercial_activity'),
                'website':req.get('website'),
                'facebook_hyperlink':req.get('facebook_hyperlink'),
                'instagram_hyperlink':req.get('instagram_hyperlink'),
                'twitter_hyperlink':req.get('twitter_hyperlink'),
                'linkedin_hyperlink':req.get('linkedin_hyperlink'),
                'tiktok_hyperlink':req.get('tiktok_hyperlink'),
                'bank_name':req.get('bank_name'),
                'bank_number':req.get('bank_number'),
                'wallet_name':req.get('wallet_name'),
                'wallet_number':req.get('wallet_number'),
                'default_payment_type':req.get('payment_type_id'),
                'group_id': res.id or False,
                'business_work_category': req.get('business_work_category')
                })
            vals = {
                'user': result,
                }
            if 'email' in req:
                request.env['rb_delivery.action'].get_reg_action(req.get('email'))
            return request.render("rb_delivery.tmp_customer_form_success", vals)
        else:
            return request.render("rb_delivery.tmp_customer_form_fail")

    @http.route('/api/order_tracking', type='json', methods=['POST', 'GET'], auth='public', website=True, csrf=False,save_session=False)
    def get_order_status_history(self, **rec):

        order_log_list = []
        order_track_id = rec['order_seq']
        if order_track_id:
            order_track_id=order_track_id.strip()
            statuses = request.env['rb_delivery.status'].sudo().search([('show_in_order_track','=',True)])
            statuses_list=statuses.mapped('id')
            order_log_dic = request.env['rb_delivery.order_logs'].sudo().search(['|',('order_id.reference_id','=',order_track_id),('order_id.sequence', '=', order_track_id),('field_id.name','=','state_id'),('new_value_id','in',statuses_list)],order="create_date desc")
            if len(order_log_dic) >0 :
                for order_log in order_log_dic:
                    order_log_list.append({
                        'sequence': order_log.order_id.sequence,
                        'reference_id': order_log.order_id.reference_id,
                        'previous_status': order_log.old_value,
                        'status': order_log.new_value,
                        'created_on': order_log.create_date
                    })

                args = {'code':200,'success': True, 'message': 'Order Data Successfully fetched ','result': order_log_list }
            else :
                order_log_dic = request.env['rb_delivery.order'].sudo().search(['|',('reference_id','=',order_track_id),('sequence', '=', order_track_id),('state_id','in',statuses_list)],order="create_date desc",)
                if len(order_log_dic) >0 :
                    for order_log in order_log_dic:
                            order_log_list.append({
                                'sequence': order_log.sequence,
                                'reference_id': order_log.reference_id,
                                'previous_status': request.env['rb_delivery.status'].sudo().search([('name','=',order_log.state)],limit=1).title,
                                'status': request.env['rb_delivery.status'].sudo().search([('name','=',order_log.state)],limit=1).title,
                                'status_code': request.env['rb_delivery.status'].sudo().search([('name','=',order_log.state)],limit=1).name,
                                'note': order_log.note,
                                'created_on': order_log.create_date
                            })

                    args = {'code':200,'success': True, 'message': 'Order Data Successfully fetched ','result': order_log_list }
                else:
                    args = {'code':200,'success': False, 'message': 'Order does not exist in the history'}

            return args
        else :
            args = {'code':401,'success': False, 'message': 'Order does not exist'}
            return args

    @http.route(['/order_tracking','/order_tracking/<string:order_sequence>'], type='http', methods=['POST', 'GET'], auth='public', website=True, csrf=False,save_session=False)
    def get_order_logs(self,order_sequence=False, **req):

        order_log_list = []
        order_track_id = req.get('order_seq')
        if order_sequence:
            order_track_id = order_sequence
        if order_track_id:
            # get all status status that can be shown
            order_track_id=order_track_id.strip()
            statuses = request.env['rb_delivery.status'].sudo().search([('show_in_order_track','=',True)])
            statuses_list=statuses.mapped('id')
            order_log_dic = request.env['rb_delivery.order_logs'].sudo().search(['|',('order_id.reference_id','=',order_track_id),('order_id.sequence', '=', order_track_id),('field_id.name','=','state_id'),('new_value_id','in',statuses_list)],order="create_date desc")

            for order_log in order_log_dic:
                     order_log_list.append({
                         'sequence': order_log.order_id.sequence,
                         'reference_id': order_log.order_id.reference_id,
                         'previous_status': request.env['rb_delivery.status'].sudo().search([('id','=',order_log.old_value_id)],limit=1).title,
                         'status': request.env['rb_delivery.status'].sudo().search([('id','=',order_log.new_value_id)],limit=1).title,
                         'created_on': order_log.create_date
                     })
        return request.render("rb_delivery.order_tracking_template", {
            'orders': order_log_list,
            'order_seq': order_track_id
        })


    @http.route('/forgot_password', type='http', methods=['POST', 'GET'], auth='public', website=True, csrf=False,save_session=False)
    def forgot_password(self, **req):
        import string
        import random
        values = req
        message = ''
        phone_number = ''
        email = ''
        user = ''
        if 'phone_number' in values and values['phone_number']:
            phone_number = values['phone_number']
            user = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',phone_number)],limit=1)
            if user:
                result_str = ''.join(random.choice(string.ascii_letters) for i in range(8))
                user.sudo().write({'password':result_str})
                message = 'Your new password has been set successfully: '
                try:
                    request.env['rb_delivery.sms'].sudo().send_sms(message + result_str,phone_number,raise_exception=True)
                except Exception as e:
                    message = 'Contact your administrator to change your password'
            else:
                message = 'There is no user with phone number ' + phone_number
        elif 'email' in values and values['email']:
            email = values['email']
            user = request.env['rb_delivery.user'].sudo().search([('email','=',email)],limit=1)
            if user:
                result_str = ''.join(random.choice(string.ascii_letters) for i in range(8))
                user.sudo().write({'password':result_str})
                message = 'Your new password has been set successfully: '
                try:
                    request.env['rb_delivery.notification_center'].sudo().send_email_forget_password(email,message + result_str,user,'New Password')
                except Exception as e:
                    message = 'Contact your administrator to change your password'
            else:
                message = 'There is no user with email ' + email
        else:
            message = 'Please set your credential information'
        return request.render("rb_delivery.forgot_passowrd_template", {
            'message': message
        })

    @http.route('/forgot_password_mobile', type='json', methods=['POST', 'GET'], auth='public', website=True, csrf=False,save_session=False)
    def forgot_password_mobile(self, **req):
        import string
        import random
        phone_number = req.get('phone_number')
        by_sms = req.get('by_sms')
        by_email = req.get('by_email')
        message = ''
        if phone_number:
            # get all status status that can be shown
            phone_number=phone_number.strip()
            user = request.env['rb_delivery.user'].sudo().search([('mobile_number','=',phone_number)])
            if user:
                email = user.email
                result_str = ''.join(random.choice(string.ascii_letters) for i in range(8))
                user.sudo().write({'password':result_str,'forgot_password':True})
                message = 'Your new password has been set successfully: '
                if by_email == True:
                    request.env['rb_delivery.notification_center'].sudo().notification([user], [email], [],phone_number, 'New Password', message + result_str, False, True, False,None,None,notification_group_type="Password")
                if by_sms == True:
                    request.env['rb_delivery.sms'].sudo().send_sms(message + result_str,phone_number)
            else:
                return {"code": 400, "message":'Invalid mobile number'}


        return  {"code": 200,'message': message,'new_password': result_str,'phone_number':phone_number}



    @http.route('/olivery/sign_up/form', type='http', methods=['POST', 'GET'], auth='public', website=True, csrf=False,save_session=False)
    def olivery_signup_form(self, **req):
        areas = []
        payment_types = []
        areas = request.env['rb_delivery.area'].sudo().search([['show_in_register','=',True]])
        payment_types = request.env['rb_delivery.payment_type'].sudo().search([])
        terms_and_conditions = ''
        company = request.env['res.company'].sudo().search([])[0]
        terms_and_conditions = company.terms_and_conditions
        business_work_categories = request.env['rb_delivery.business_work_category'].sudo().search([])
        letters = string.ascii_letters
        result_str = ''.join(random.choice(letters) for i in range(8))
        email_required = request.env['rb_delivery.client_configuration'].sudo().get_param('email_registration')
        social_media_visibility =request.env['rb_delivery.client_configuration'].sudo().get_param('register_social_media_visibility')
        business_work_category_visibility = request.env['rb_delivery.client_configuration'].sudo().get_param('register_business_work_category_visibility')
        return request.render("rb_delivery.olivery_signup_template", {'areas': areas,'payment_types':payment_types,'terms_and_conditions':terms_and_conditions,'email_required':email_required,'generated_password':result_str,'social_media_visibility':social_media_visibility,'business_work_categories':business_work_categories,'business_work_category_visibility':business_work_category_visibility})

    @http.route('/olivery/terms_and_conditions', type='http', methods=['POST', 'GET'], auth='public', website=True, csrf=False,save_session=False)
    def olivery_terms_and_conditions(self, **req):
        terms_and_conditions = ''
        company = request.env['res.company'].sudo().search([])[0]
        terms_and_conditions = company.terms_and_conditions
        return request.render("rb_delivery.olivery_terms_and_conditions", {'terms_and_conditions':terms_and_conditions})

    @http.route('/location/<string:order_id>', type='http', methods=['POST', 'GET'], auth='public', website=True, csrf=False,save_session=False)
    def location_order_form(self, order_id):
       payment_types = request.env['rb_delivery.payment_type'].sudo().search([])
       return request.render("rb_delivery.olivery_order_location_template", {'order_id': order_id,'payment_types':payment_types})

    @http.route('/track_driver/<string:tracking_uid>', type='http', methods=['POST', 'GET'], auth='public', website=True, csrf=False,save_session=False)
    def live_track_driver(self, tracking_uid):
       return request.render("rb_delivery.olivery_driver_live_tracking_template", {'tracking_uid': tracking_uid})

    @http.route('/get_driver_location/<string:tracking_uid>', type='http', auth='public', website=True, csrf=False, methods=['POST', 'GET'],save_session=False)
    def get_driver_location(self, tracking_uid, **kwargs):
        order = request.env['rb_delivery.order'].sudo().search([['tracking_uid','=',tracking_uid]])
        if order and order.assign_to_agent:
            if order.assign_to_agent.longitude and order.assign_to_agent.latitude:
                res = json.dumps({'success':True,'code':200,'longitude':order.assign_to_agent.longitude,'latitude':order.assign_to_agent.latitude})
            else:
                res = json.dumps({'success':False,'code':401,'reason':_('Driver is not sharing location!')})
        else:
            res = json.dumps({'success':False,'code':402,'reason':_('Link is expired!')})
        return res

    @http.route('/order/share/location', type='http', methods=['POST'], auth='public', website=True, csrf=False,save_session=False)
    def location_order_submit(self, **req):
        order_id = http.request.params['order_id']
        lat = http.request.params['lat']
        lon = http.request.params['lon']
        payment_type_id = http.request.params['payment_type_id']
        note = http.request.params['note']

        if order_id:
            order = request.env['rb_delivery.order'].sudo().search([('id','=',order_id)])
            order.write({'latitude':lat,'longitude':lon,'payment_type':payment_type_id,'note':note})

            return request.render("rb_delivery.tmp_customer_form_success_location")
        else:
            return request.render("rb_delivery.tmp_customer_form_fail_location")

    @http.route('/rb_delivery/mobile_translation/ar.json/', auth='public',save_session=False)
    def get_mobile_ar_translation(self, **kw):
        import base64
        config = request.env['rb_delivery.client_configuration'].sudo().search_read([('key','=','mobile_ar_translation_file')],['key','value','platform_type','text','status','group_ids','attachment'])
        ar = config[0]['attachment']

        if ar:
             return base64.decodestring(ar)
        else:
            return {}


    @http.route('/rb_delivery/mobile_translation/fr.json/', auth='public',save_session=False)
    def get_mobile_fr_translation(self, **kw):
        import base64
        config = request.env['rb_delivery.client_configuration'].sudo().search_read([('key','=','mobile_fr_translation_file')],['key','value','platform_type','text','status','group_ids','attachment'])
        fr = config[0]['attachment']

        if fr:
             return base64.decodestring(fr)
        else:
            return {}

    @http.route('/rb_delivery/sync_translation', auth='public', type='json', methods=['POST'], csrf=False)
    def sync_translation_files(self):
        data = json.loads(request.httprequest.data)

        if not data or not data.get('translations') or 'is_force_load' not in data:
            return {"success": False, "error": "Missing required attributes in the request body."}

        translations = data['translations']
        is_force_load = data['is_force_load']
        results = []

        for translation in translations:
            first_key = next(iter(translation))
            config = request.env['rb_delivery.client_configuration'].sudo().search([('key', '=', first_key)], limit=1)

            if not config or not config.attachment:
                results.append({"success": False, "error": f"Configuration not found for language {first_key}"})
                continue

            try:
                # Decode existing JSON data
                binary_data = base64.b64decode(config.attachment)
                json_data = json.loads(binary_data.decode('utf-8'))

                # Update the JSON data with new terms
                terms = ast.literal_eval(translation[first_key])
                for term_key, term_value in terms.items():
                    if term_key in json_data and is_force_load or term_key not in json_data:
                        json_data[term_key] = term_value

                # Encode updated JSON data with pretty-printing
                new_json_str = json.dumps(json_data, ensure_ascii=False, indent=2)
                new_binary_data = base64.b64encode(new_json_str.encode('utf-8'))
                config.write({'attachment': new_binary_data})

                results.append({"success": True, "key": first_key})
            except Exception as e:
                error_message = str(e)
                _logger.error("Error updating configuration for %s: %s", first_key, error_message)
                results.append({"success": False, "error": error_message})

        return results

    @http.route('/rb_delivery/mobile_translation/en.json/', auth='public',save_session=False)
    def get_mobile_en_translation(self, **kw):
        config = request.env['rb_delivery.client_configuration'].sudo().search_read([('key','=','mobile_en_translation_file')],['key','value','platform_type','text','status','group_ids','attachment'])
        en = config[0]['attachment']

        if en:
             return base64.decodestring(en)
        else:
            return {}

    @http.route('/rb_delivery/mobile_translation/he.json/', auth='public',save_session=False)
    def get_mobile_he_translation(self, **kw):
        config = request.env['rb_delivery.client_configuration'].sudo().search_read([('key','=','mobile_he_translation_file')],['key','value','platform_type','text','status','group_ids','attachment'])
        he = config[0]['attachment']

        if he:
             return base64.decodestring(he)
        else:
            return {}

    @http.route('/update_location', auth='user', type="json")
    def new_location(self, **req):
        coords = req.get('coords')
        user_id = req.get('extras')['user_id']
        values={
            'user_id':user_id,
            'longitude':coords['longitude'],
            'latitude':coords['latitude']
        }
        # no need to update the user lat & long here beacause we update it on create location
        location = request.env['rb_delivery.location'].create(values)


        return  {"code": 200,'message': "updated the location success",'id':location.id}

    @http.route('/post_install_modules', auth='public', type="json",save_session=False)
    def post_install_modules(self, **req):

        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        additional_modules = []
        if 'additional_modules' in http.request.params and http.request.params['additional_modules']:
            additional_modules = http.request.params['additional_modules']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args
        installation_additional_modules_response = {}
        installation_modules_response = request.env['rb_delivery.post_init_modules_installation'].auto_install_modules(['olivery_website_templates','olivery_theme_basic','olivery_demo_data','olivery_beep_feature','olivery_dynamic_dashboard','olivery_dynamic_integration','olivery_recepient','olivery_vhub','olivery_excel_advance'])
        if len(additional_modules) > 0:
            installation_additional_modules_response = request.env['rb_delivery.post_init_modules_installation'].auto_install_modules(additional_modules)
        return installation_modules_response.update(installation_additional_modules_response)


    @http.route('/check_install_modules', auth='public', type="json",save_session=False)
    def check_install_modules(self,**req):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        additional_modules = []
        if 'additional_modules' in http.request.params and http.request.params['additional_modules']:
            additional_modules = http.request.params['additional_modules']
        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args

        auto_install_modules = [
            'olivery_website_templates','olivery_theme_basic', 'olivery_demo_data', 'olivery_beep_feature',
            'olivery_dynamic_dashboard', 'olivery_dynamic_integration', 'olivery_recepient','olivery_vhub','olivery_excel_advance'
        ]
        if len(additional_modules) > 0:
            auto_install_modules = auto_install_modules + additional_modules
        installed_modules = []

        for module_name in auto_install_modules:
            module = request.env['ir.module.module'].search_read([('name', '=', module_name)], ['state'], limit=1)
            if module and module[0].get('state') == 'installed':
                installed_modules.append(module_name)

        if len(installed_modules) < len(auto_install_modules):
            pending_install_modules = [module for module in auto_install_modules if module not in installed_modules]
            return {
                'code': 200,
                'state': 'pending',
                'message': _('Installed modules: {} \nPending modules: {}').format(installed_modules, pending_install_modules)
            }
        else:
            return {
                'code': 200,
                'state': 'success',
                'message': _('{} installed successfully').format(installed_modules)
            }

    @http.route('/install_delivery_configuration', auth='public', type="json",save_session=False)
    def install_delivery_configuration(self,**req):
        data = http.request.params
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']

        # authenticate
        uid = request.session.authenticate(db, login, password)

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args
        if 'demo_data_tags' in data and data['demo_data_tags']:
            country_domain=[]
            packages_names_domain = []
            domain=[]
            demo_data_tags = data['demo_data_tags']
            if 'country_tag' in demo_data_tags and demo_data_tags['country_tag']:
                country_tag = demo_data_tags['country_tag']
                country_tag_id = request.env['olivery_demo_data.package_tags'].sudo().search([['name','=',country_tag],['type','=','country']]).id
                if not country_tag_id:
                    return {'code':404,'state' : 'fail', 'message':_("%s tag not found in country tags")%(country_tag)}
                country_domain = [('country_tags','in',[country_tag_id])]
            if 'packages_names' in demo_data_tags and demo_data_tags['packages_names']:
                packages_names = demo_data_tags['packages_names']
                for package_name in packages_names:
                     packages_names_domain.append(('name','=',package_name))
            domain = domain+country_domain+packages_names_domain
            if len(domain) > 1:
                or_condition_number = len(domain) - 1
                for i in range(or_condition_number):
                    domain.insert(0, '|')
            try:
                package_ids = request.env['olivery_demo_data.demo_package'].sudo().search(domain).ids
                data_creator = request.env['olivery_demo_data.demo_creator'].sudo().create({
                    'demo_package_ids':[[6,0,package_ids]]
                })
                response = data_creator.create_demo_data()
            except Exception as e:
                return {
                    'code' : 404,'state' : 'fail', 'message' : e
                }

            return {
                'code' : 200,'state' : 'success', 'message' : 'success install '+', '.join(demo_data_tags['packages_names']) +' configuration'
            }

    @route(['/selected/language'], type='json', auth='public',save_session=False)
    def user_selected_language(self, selected_language):
        """
        To switch the user language
        :param selected_language: string of language short code
        """
        request.env.user.lang = selected_language

    @http.route('/check_po_file', auth='public', type="json",save_session=False)
    def get_arabic_file_path(self, **kw):

        uid = request.session.uid

        if uid == False:
            args = {'code':403,'fail': True, 'message': _('Failed to authenticate.')}
            return args
        modules_to_check = request.env['ir.module.module'].sudo().search([['state','in',['installed', 'to install', 'to upgrade']]])
        duplicate_items = {}
        for module in modules_to_check :
            po_file_path = get_module_resource(module.name, 'i18n', 'ar_SY' + '.po')
            if po_file_path:
                with open(po_file_path, 'r', encoding='utf-8') as f:
                    po_content = f.read()

                entries = [entry.strip() for entry in po_content.split('\n\n') if entry.strip()]

                po_dict = {}

                pattern = re.compile(r'#\. module: (.+)\n(?:#: model_terms:(.+)\n|#: model:(.+)\n)msgid "(.*?)"\nmsgstr "(.*?)"', re.DOTALL)
                for entry in entries:

                    match = pattern.match(entry)

                    module_name = match.group(1) if match else None
                    model_term = match.group(2) if match else None
                    model = match.group(3) if match else None
                    msgid = match.group(4) if match else None
                    msgstr = match.group(5) if match else None

                    if msgid is not None :
                        if msgid in po_dict and po_dict[msgid]['model_term'] and po_dict[msgid]['model_term'] == model_term:
                            duplicate_items[msgid] =  {'value' :msgstr,'module_name' : module.name }
                        elif msgid in po_dict and po_dict[msgid]['value'] == msgstr:
                            duplicate_items[msgid] =  {'value' :msgstr,'module_name' : module.name }
                        else:
                            for entry in po_dict.values() :
                                if entry['model'] and entry['model'] is not None and entry['model'] == model:
                                    duplicate_items[msgid] =  {'value' :msgstr,'module_name' : module.name }

                            po_dict[msgid] = {'value' :msgstr,'model_term' : model_term,'model' : model }

        if len(duplicate_items.items()) > 0:
            return {'code':200,'success':False,'message':duplicate_items}
        else:
            return {'code':200,'success':True,'message':'PO File Valid'}

    @http.route('/pre_deploy_rc_next', auth='public', type="json",save_session=False)
    def pre_deploy_rc_next(self, **req):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        uid = request.session.authenticate(db, login, password)
        args = {}
        if uid == False:
            args = {'code':403,'success': False, 'message': _('Failed to authenticate.')}
        try:
            set_to_user_current_branch = request.env['rb_delivery.status_action'].search([('name','=','set_to_user_current_branch')])
            if set_to_user_current_branch and set_to_user_current_branch.id:
                set_to_user_current_branch.unlink()

            collection_show_to_branch = request.env['rb_delivery.status_related_field'].search([('name','=','collection_show_to_branch')])
            if collection_show_to_branch and collection_show_to_branch.id:
                collection_show_to_branch.unlink()

            collection_show_current_branch = request.env['rb_delivery.status_related_field'].search([('name','=','collection_show_current_branch')])
            if collection_show_current_branch and collection_show_current_branch.id:
                collection_show_current_branch.unlink()

            show_current_branch = request.env['rb_delivery.status_related_field'].search([('name','=','show_current_branch')])
            if show_current_branch and show_current_branch.id:
                show_current_branch.unlink()

            show_to_branch = request.env['rb_delivery.status_related_field'].search([('name','=','show_to_branch')])
            if show_to_branch and show_to_branch.id:
                show_to_branch.unlink()

            select_route = request.env['ir.actions.act_window'].search([('res_model','=','rb_delivery.select_route')])
            if select_route and select_route.id:
                select_route.unlink()

            change_branch_wizard = request.env['ir.actions.act_window'].search([('res_model','=','rb_delivery.change_branch_wizard')])
            if change_branch_wizard and change_branch_wizard.id:
                change_branch_wizard.unlink()

            default_theme_color = request.env['rb_delivery.client_configuration'].search([('key','=','default_theme_color')])
            if default_theme_color and default_theme_color.text:
                default_theme_color.write({'text':False})

            args={'code':200,'success': True, 'message': _('Success Removing window actions and configurations')}
        except Exception as e:
            args={'code':400,'success': False, 'message': _('Failed to removing window actions and configurations'+e.args[0])}

        return args

    @http.route('/post_deploy_rc_next', auth='public', type="json",save_session=False)
    def post_deploy_rc_next(self, **req):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        uid = request.session.authenticate(db, login, password)
        args = {}
        if uid == False:
            args = {'code':403,'success': False, 'message': _('Failed to authenticate.')}
        try:
            all_window_actions = request.env['ir.actions.act_window'].search([])
            if len(all_window_actions) > 0:
                all_window_actions.unlink()

            context = {"lang": "en_US","tz": False,"uid": uid,"ir.ui.menu.full_list": True}
            all_menu_items = request.env['ir.ui.menu'].with_context(context).search_read([], ["sequence", "complete_name", "name"])
            if len(all_menu_items) > 0:
                menu_item_ids = []
                for menu_item in all_menu_items:
                    menu_item_ids.append(menu_item['id'])
                request.env['ir.ui.menu'].browse(menu_item_ids).unlink()
            base_module = request.env['ir.module.module'].search([('name','=','base')])
            base_module.button_immediate_upgrade()
            args={'code':200,'success': True, 'message': _('Success Removing all window actions and all menus')}
        except Exception as e:
            args={'code':400,'success': False, 'message': _('Failed to removing all window actions and all menus'+e.args[0])}
        return args

    @http.route('/get_installed_modules', auth='public', type="json",save_session=False)
    def get_installed_modules(self, **kw):
        installed_modules = []
        args = {}
        if request.jsonrequest:
            olivery_modules = request.env['ir.module.module'].sudo().search_read([('state', '=', 'installed'),'|','|',('author','=','Olivery'),('author','=','Rubik'),('author','=','Rubik Company')], ['name'])
            for module in olivery_modules:
                installed_modules.append(module['name'])
            args = {'code':200,'success': True, 'result':installed_modules}
        return args

    @http.route(['/download/report/<record_ref>'], type='http', auth="user")
    def download_binary(self, record_ref, **kwargs):
        record = request.env['rb_delivery.report_job_queue'].sudo().search([('reference', '=', record_ref)])
        if not record or not record.report_data:
            return request.not_found()

        content_type = 'application/pdf'
        sequences = []
        if record.active_ids and record.model:
            ids = ast.literal_eval(record.active_ids)
            records = request.env[record.model].browse(ids)
            sequences = [rec.sequence for rec in records]
        filename = ','.join(sequences)+ '.pdf' if len(sequences)>0 else record.report_name + '.pdf' if record.report_name else "downloaded_file.pdf"

        return request.make_response(
            base64.b64decode(record.report_data),
            headers=[
                ('Content-Type', content_type),
                ('Content-Disposition', f'attachment; filename="{filename}"')
            ]
        )
    
    @http.route('/get_untranslated_terms', auth='public', type="json",save_session=False)
    def get_untranslated_terms(self, **rec):
        domain_input = http.request.params.get('domain', [])
        if not isinstance(domain_input, list):
            return {'error': _('Invalid domain input. Expected a list.')}
        domain = ["|",["value","=",False],["value","=",""]]
        untranslated_terms = request.env['ir.translation'].sudo().search_read(domain+domain_input, ['source'])

        return untranslated_terms
    
    @http.route('/api/create_archived_order', auth='none', type="json")
    def create_archive_order(self, **kwargs):
        db = http.request.params['db']
        password = http.request.params['password']
        login = http.request.params['login']
        del kwargs['login']
        del kwargs['password']
        del kwargs['db']
        uid = request.session.authenticate(db, login, password)
        if not uid:
            return {'code': 403, 'fail': True, 'message': _('Failed to authenticate.')}
        if request.env.user.has_group('rb_delivery.role_super_manager') or request.env.user.has_group('rb_delivery.role_accounting'):
            try:
                rec = request.env['rb_delivery.archive_order'].sudo().create(kwargs)
                return {'success': _('Record created successfully')}
            except Exception as e:
                return {'error': _('An unexpected error occurred: %s' % str(e))}
        return {'error': _('Access denied')}
    
    @http.route('/api/get_image', auth='none', type='http', methods=['GET'])
    def get_image(self, id=None, field=None, model=None, **kwargs):
        if not id or not field or not model:
            return {'error': _('Missing parameters: \'id\', \'field\', or \'model\'.')}, 404
        try:
            record = request.env[model].sudo().browse(int(id))
            if not record.exists():
                return {'error': _('Record with ID {id} not found in model {model}.')} , 404
            image_data = record[field]
            if not image_data:
                return _('No data found in the specified field.'), 404
            headers = [
                ('Content-Type', 'image/png'),  # Adjust MIME type based on your image format
                ('Content-Disposition', f'inline; filename="image_{id}.png"')
            ]
            return request.make_response(base64.b64decode(image_data), headers=headers)
        except Exception as e:
            return request.make_response(f"Error: {str(e)}", status=500)
    

class Home(WebHome):
    @http.route('/web/login', type='http', auth='public', website=True)
    def web_login(self, redirect=None, **kw):
        # Check if the user is already logged in (request._uid exists)
        if request._uid:
            # User is logged in, redirect to /web
            return super(Home, self).web_login(redirect='/web', **kw)
        
        # If user is not logged in, proceed with the original login behavior
        return super(Home, self).web_login(redirect=redirect, **kw)
    
